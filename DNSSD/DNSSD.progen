vc.project.guid = D9257FF3-3A9A-41F6-B60E-D077EFF94186
vc.project.name = DNSSD
vc.project.target = Poco${vc.project.name}
vc.project.type = library
vc.project.pocobase = ..
vc.project.outdir = ${vc.project.pocobase}
vc.project.platforms = Win32, x64, WinCE
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = ${vc.project.name}_vs90.vcproj
vc.project.compiler.include = ..\\Foundation\\include;..\\Net\\include
vc.project.compiler.defines = 
vc.project.compiler.defines.shared = ${vc.project.name}_EXPORTS
vc.project.compiler.defines.debug_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.defines.release_shared = ${vc.project.compiler.defines.shared}
vc.project.linker.dependencies.Win32 = ws2_32.lib iphlpapi.lib
vc.project.linker.dependencies.x64 = ws2_32.lib iphlpapi.lib
vc.project.linker.dependencies.WinCE = ws2.lib iphlpapi.lib
vc.solution.create = true
