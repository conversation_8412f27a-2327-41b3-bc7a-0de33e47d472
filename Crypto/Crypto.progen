vc.project.guid = EEEE7259-32E9-4D56-B023-C733940AB2A0
vc.project.name = Crypto
vc.project.target = Poco${vc.project.name}
vc.project.type = library
vc.project.pocobase = ..
vc.project.outdir = ${vc.project.pocobase}
vc.project.platforms = Win32
vc.project.vcpkg = true
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = ${vc.project.name}_vs90.vcproj
vc.project.compiler.include = ${vc.project.pocobase}\\Foundation\\include
vc.project.compiler.defines =
vc.project.compiler.defines.shared = ${vc.project.name}_EXPORTS
vc.project.compiler.defines.debug_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.defines.release_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.project.linker.dependencies = ws2_32.lib iphlpapi.lib
vc.project.linker.dependencies.debug_shared =
vc.project.linker.dependencies.release_shared =
vc.project.linker.dependencies.debug_static_md = Crypt32.lib
vc.project.linker.dependencies.release_static_md = Crypt32.lib
vc.project.linker.dependencies.debug_static_mt = Crypt32.lib
vc.project.linker.dependencies.release_static_mt = Crypt32.lib
vc.solution.create = true
vc.solution.include = testsuite\\TestSuite
