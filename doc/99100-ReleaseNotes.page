POCO C++ Libraries Release Notes
AAAIntroduction

!!!Release 1.14.2

!!Summary of Changes

This is a bugfix release.

!!Breaking Changes

  - GH #4906 NetSSL_OpenSSL: non-blocking functions return value

!!Security Fixes

  - GH #4899 Upgrade bundled libexpat to 2.7.1 [fixes CVE]
  - GH #4926 Crash in Poco::Net::NTLMCredentials::parseChallengeMessage

!!Features, Enhancements and Third Party Updates

  - GH #4892 TCPServer continues to accept connections after stop()
  - GH #4911 Upgrade bundled libexpat to 2.7.1
  - GH #4907 Upgrade bundled SQLite to 3.49.1
  - GH #4905 XML fuzzing memory leak
  - PR #4937 CMake: make utf8proc, PCRE2, zlib and expat private dependencies

!!Bug Fixes and Improvements

  - GH #4923 Poco::Data::SessionPool::dead() must check idle, not active, sessions
  - GH #4886 Poco::Dynamic::Var conversion to floating point does not handle precision checks correctly for negative numbers.
  - GH #4884 WebSocket receiveFrame() keeps returning the same frame when no payload (flags/header only)
  - GH #4875 prebuild does not work as intended
  - GH #4935 HTTP server/client request body RFC compliance
  - GH #4930 Poco::Net::PollSet.cpp:188 compare between int and size_t is not correct.
  - GH #4920 Adapt to avoid Clang warning deprecated-enum-enum-conversion
  - GH #4915 [BUG] A SEGV at `Net/src/MultipartReader.cpp:164:1`
  - PR #4933 Windows: report strings for all PROCESSOR_ARCHITECTURE_* constants in osArchitectureImpl()
  - PR #4896 fix(TCPServer): continues to accept connections after stop()


!!!Release 1.14.1

!!Summary of Changes

This is a bugfix release.

!!Features, Enhancements and Third Party Updates

  - GH #4833 Add start() method to the SocketReactor
  - GH #4825 Poco::Net::HTTPResponse: add replaceCookie() and removeCookie()

!!Bug Fixes and Improvements

  - GH #4866 Poco::icompare(str, pos, ptr) out of bounds error if pos > str.size()
  - GH #4864 OpenSSL: Need to add openssl/applink.c to Windows executables using OpenSSL
  - GH #4859 Poco::NumberParser::tryParseHex raise Poco::SyntaxException exception.
  - GH #4852 Issue concerning Net/CMakeLists.txt detection of HAVE_SENDFILE
  - GH #4850 WebSocket: non-blocking receiveFrame()/receiveBytes() with TLS connection may get stuck receiving header
  - GH #4840 latest 1.14.0 release fails to build on armv7hl
  - GH #4832 Missing parameter for enabling FTS5
  - GH #4831 httpS server seems to not work in v1.14
  - GH #4828 POCO_HAVE_CXXABI_H does not check for existence of header file
  - GH #4817 Windows Static Build Problem / Changed behaviour of POCO_MT (CMake 3.15)
  - GH #4806 Poco 1.14.0 now requires libatomic
  - GH #4798 PocoFoundationConfig.cmake is missing Utf8Proc dependency
  - GH #3304 Windows Eventviewer not able to find PocoFoundation DLL
  - PR #4865 Fix openssl cmake applink
  - PR #4863 Fix StreamSocket::sendFile()
  - PR #4862 fix(cmake): Add back missing compiler definitions for static_build in the generated CMake files
  - PR #4845 fix(Net) Use of Uninitialized value in NTLMCredentials::parseChallengeMessage
  - PR #4838 fix(Net) bad mask with odd number of bytes
  - PR #4836 Fix typo that leads to the use of freed memory
  - PR #4834 Missing parameter for enabling FTS5
  - PR #4822 MongoDB: use constants instead of typed enum in OpMsgMessage
  - PR #4818 Modifications of Poco::Any in an attempt to fix OSS Fuzz report
  - PR #4849 fix(templates): Corrected explicit template instantiations
  - PR #4811 fix(cmake): remove libatomic dependency
  - PR #4805 fix(cmake): correct typo AVHAI -> AVAHI
  - PR #4803 Updated CMake to also include the generated pocomessage.rc File


!!!Release 1.14.0

!!Summary of Changes

This release marks many functions and classes as deprecated with C++ [[deprecated]]
attribute. Warnings can be silenced by defining POCO_SILENCE_DEPRECATED.

Deprecated functionality will be removed in one of the next releases.

Minimal supported standard is C++17 since version 1.13. Decent part of the
source code is modernised in this release.

Bundled software libraries are updated to latest versions:

  - zlib 1.3.1
  - expat 2.6.4
  - pcre2 10.44
  - libpng 1.6.43
  - SQLite 3.47.1

Poco::Foundation:

  - Support for Version 6 and Version 7 UUIDs

Poco::MongoDB:

  - Obsolete wire-protocol (pre 3.6) that was removed from MongoDB in version 5.1
    is obsolete in this version of Poco. It will be removed in one of next versions. Update code to use
    OpMsg-based interface.

Poco::Data:

  - Full Nullable support (including bulk inserts)
  - ODBC SQL Server big strings support


!!Breaking Changes

  - GH #4426 Mark deprecated code for removal

!!Security Fixes

  - GH #4760 Upgrade libexpat to release 2.6.4
  - GH #4690 Net: stack-buffer-overflow if HTTP request contains a header with invalid UTF32 sequence
  - GH #4687 Net::MailMessage: Double free if Content-Disposition header is empty
  - GH #4629 XML: fuzzing stack overflow
  - GH #4123 DoS vulnerability in XML/src/xmlparse.cpp
  - GH #4478 Upgrade bundled PCRE2 to 10.44
  - PR #4677 libpng version 1.6.43

!!Features, Enhancements and Third Party Updates

  - PR #4787 NetSSL_OpenSSL and NetSSL_Win: non-blocking support, shutdown behavior fix
  - PR #4681 zlib: Version 1.3.1 in module PDF (#4582)
  - PR #4788 Update bundled SQLite to version 3.47.1 (released 2024-11-25)
  - GH #4669 Upgrade JSON parser
  - GH #4580 Poco::UUID/UUIDGenerator: add support for Version 6 and Version 7 UUIDs
  - PR #4724 Hide zlib and expat libs from the user of Poco libraries (replaces #4579)
  - GH #4766 ProcessRunner sync
  - GH #4750 DBLogger sample
  - GH #4712 Error code for pthread_mutex_lock failure
  - GH #4710 UTF8::normalize()
  - GH #4680 Poco::Path::forDirectory("C:") throws if the path is disk-letter only
  - GH #4556 Overriding SocketReactor::run() is not reasonably possible
  - GH #4553 Poco::Logger can not output __FILE__ and __LINE__
  - GH #4544 The load balancing issue in Poco::ActiveThreadPool
  - GH #4502 Optional and Nullable Improvements
  - GH #4431 Consolidate LogFile implementation to use FileOutputStream
  - GH #4423 `Dynamic::Var` silently loses precision on int->float conversion
  - GH #4230 Poco::Data fixes and improvements
  - GH #3997 operator<<() for SocketAddress doesn't work with ADL
  - GH #3801 ODBC: DataFormatException getting Time value from SQL Server
  - GH #3656 Parse json into specified descendant class
  - GH #2808 Failed to insert Poco::Nullable< Poco::Data::Date> data into MSSQL using ODBC
  - GH #1540 Poco::DateTime uses assertions for validation
  - GH #4769 pocoNetworkInitializer - change fixed path (Windows Platform related)
  - GH #4716 Logging: JSONFormatter
  - GH #4692 Stacktrace
  - GH #4643 `ServerApplication` callbacks
  - GH #4632 Add `prebuild` action to make build
  - GH #4630 Make `Util::Application::getApplicationPath()` and `findAppConfigFile()` protected
  - GH #4559 ServerApplication has no pidfile option on windows
  - GH #4536 Serializable Isolation level for SQLite Databases
  - GH #4529 SQLChannel stops logging on LoggingSubsystem shutdown
  - GH #4493 PocoDoc: search support
  - GH #4447 Add checkers for Any holding nullptr
  - GH #4414 Improve NotificationCenter speed and usability
  - GH #4413 StreamCopier range support
  - GH #4409 Add string_view format type spec
  - GH #4365 `Poco::Data::Statement`: unified '?' placeholder support
  - GH #4341 1.12.4 version Json set enum value failed
  - GH #4324 Poco:Data::ODBC - MSSQL (n)varchar(max) length issue
  - GH #4001 Insert NULL using BULK
  - GH #3281 DTLS 1.2 support
  - PR #4777 enh(ODBC): ODBC: DataFormatException getting Time value from SQL Serv…
  - PR #4774 Non-blocking sockets support (TLS, WebSocket)
  - PR #4770 feat(WindowsBuild): customizable path to include for pocoNetworkInitializer #4769
  - PR #4755 Insert NULL using BULK #4001
  - PR #4748 Activity now sets _running flag to false when it finishes or throws.
  - PR #4721 fix(Data::ODBC): use connection and login timeouts in ODBC session implementation
  - PR #4714 Add missing relational operators to VarIterator
  - PR #4699 Apply patches from Debian packaging
  - PR #4693 Serializable Isolation level for SQLite Databases
  - PR #4663 Fixed incorrect SSL_CTX_set0_tmp_dh_pkey() usage
  - PR #4624 enh(Poco::ActiveThreadPool): make it easy to use correctly
  - PR #4621 enh(FileStream): Add FileStreamBuf::resizeBuffer
  - PR #4617 enh(CI): Add unbundled build on Linux.
  - PR #4616 Cppunit and data test enhancements
  - PR #4569 Allow ADL for swapping Optional values
  - PR #4563 enh(MongoDB): Document::get returns value by const reference
  - PR #3040 HTTPS proxy support
  - PR #4734 File lock
  - PR #4424 Allow using Poco::FileStream to wrap arbitrary file handles/descriptors as C++ streams
  - PR #4685 CMake: Remove possibility to build with internal OpenSSL

!!Bug Fixes and Improvements

  - GH #4773 Non-blocking sockets support (TLS, WebSocket)
  - GH #4768 Data:  warning: 'isNull' overrides a member function but is not marked 'override'
  - GH #4742 Poco does not build on AIX
  - GH #4722 libcxx: error: implicit instantiation of undefined template 'std::char_traits<unsigned char>'
  - GH #4713 replaceInPlace std::wstring
  - GH #4711 Poco::Placeholder initialization uses wrong size
  - GH #4703 File Channel Logs in UTC Despite ‘times = local’
  - GH #4695 Build error with GCC-15 (class Poco::PriorityDelegate<TObj, void, true>’ has no member named ‘_pTarget’)
  - GH #4689 SMTPClientSession: replace bare newlines in message content with CRLF.
  - GH #4668 Cross-module exception issue with pre-compiler define _HAS_EXCEPTIONS=0 used in an application
  - GH #4664 HTTPCookie Constructor Fails to Handle Discard Attribute Properly
  - GH #4648 ProcessRunner erases its PID file name
  - GH #4644 TryParse scoped ipv6 addressess for addresses enclosed in [  ]
  - GH #4634 Poco::ActiveThreadPool _targetCompleted event never reset
  - GH #4610 Incorrect setting of ciphersuites for TLSv1.3
  - GH #4592 Significant performance degradation of Poco::DateTimeParser
  - GH #4586 MacOS ARM64 build error: symbol `fdopen` is duplicated defined
  - GH #4585 MacOS ARM64 build warning: 'OS_CODE' macro redefined
  - GH #4557 NestedDiagnosticContext should be thread local
  - GH #4540 Postgres CMakeList.txt
  - GH #4535 decodeWord adds spaces at folding borders, when there are special characters encoded
  - GH #4525 RecordSet issue since 1.10.0
  - GH #4503 GitHub runner: Linux thread sanitizer tests fail with "unexpected memory mapping"
  - GH #4482 ProcessRunner does not detect launch errors
  - GH #4395 There is no way to resolve host in advance and connect to HTTPS server with SNI.
  - GH #4368 fix Oracle failing ODBC tests
  - GH #3913 Missing sources in release package - buildwin.ps1 and NetSSL_Win
  - GH #3896 TryParse scoped ipv6 addressess
  - GH #3180 Poco::Environment::osDisplayName
  - GH #2971 Poco::NamedEvent does not release System V semaphore on Linux
  - GH #2439 Issue with log purging when FileChannel compression is enabled
  - PR #4762 Properly define POCO_DLL and POCO_STATIC based on BUILD_SHARED_LIBS
  - PR #4753 Openssl DH key size
  - PR #4737 fix(cmake): fix Data::PostgreSQL target include, remove unnecessary cmake modules.
  - PR #4727 GitHub runner ubuntu 24.04 and resolve some issues
  - PR #4726 Explicitly define conditional LOB constructors for const (w)string &
  - PR #4725 Exception (VS): static assert to make sure that _HAS_EXCEPTIONS is set
  - PR #4702 SimpleRowFormatter.h: fix the build on `gcc-15` (unsatisfied `noexcept`)
  - PR #4688 Fix double free in Net::MailMessage if Content-Disposition header is empty
  - PR #4657 LibPNG Unbundled
  - PR #4652 enh(ScopedLockWithUnlock): make it more alike std::unique_lock
  - PR #4647 Use Int64 for TcpServerDispatcher::totalConnections()
  - PR #4635 fix(Poco::Zip::ZipLocalFileHeader) Fix const-correctness
  - PR #4622 fix(SimpleFileChannel): unify default "flush" to be false as it is in FileChannel
  - PR #4613 Usage modern C++ features on JSON modules (enhanced)
  - PR #4597 Fix MinGW build error
  - PR #4593 DateTimeParser Validation and Performance Improvements
  - PR #4550 enh: resolve unit test and few other warnings.
  - PR #4545 Add CACHE PATH to multi-config output directory variables
  - PR #4542 Decode word
  - PR #4417 fix(logs): synchronise log file rotation and compression.
  - PR #4085 Usage modern C++ features on JSON modules


!!!Release 1.13.3

!!Summary of Changes

This is a bugfix release.

!!Security Fixes

  - GH #4496 Upgrade bundled libexpat to 2.6.2

!!Features, Enhancements and Third Party Updates

  - GH #4488 Add Poco::Util::Timer::idle() method to check if timer has any tasks scheduled
  - GH #3807 DNS.resolve() should not be sorted in HostEntry::removeDuplicates()
  - GH #4515 Upgrade bundled SQLite to 3.45.2
  - PR #4517 Optimize Net module for Android

!!Bug Fixes and Improvements

  - GH #4505 ODBC Unicode wrappers do not check for null length pointers
  - GH #4492 Poco::BasicMemoryStreamBuf is missing seekpos()
  - GH #4486 DateTimeFormat RFC1036 Sunday name is short (should be long)
  - GH #4468 Poco::URI: don't lowercase host part if it's a Unix domain socket
  - GH #4450 Error between Poco::ActiveRecord and Poco::Data::PostgreSQL
  - GH #4435 SecureStreamSocket is not thread-safe
  - GH #4415 SecureSocketImpl::reset shouldn't close socket
  - GH #3857 Thread_POSIX.cpp shouldn't convert thread IDs to long
  - GH #3725 secure socket receiveTimeout throwing after configured timeout * 2


!!!Release 1.13.2

!!Summary of Changes

This is a bugfix release.

!!Breaking Changes

  - GH #4378 [Data] Unconditionally includes of SQLParser.h

!!Bug fixes and Improvements

  - GH #4462 Disable SQL parsing by default


!!!Release 1.13.1

!!Summary of Changes

This is a bugfix release.

!!Features and Enhancements

  - GH #4367 `SQLite` `FTS5` (full text search)
  - GH #4335 Implement patches that Debian/Ubuntu applies when preparing deb packages
  - GH #4216 Replace C string literals (const char*) with C++ std::string literals for std::string parameters.
  - GH #3890 Get rid of SingletonHolder
  - GH #2450 Why does it take the ThreadPool 10+ seconds to shutdown when there is nothing running.
  - GH #2443 FileChannel doesn't flush to disk on unix until close
  - GH #4437 Add arm cross-compile config and CI
  - PR #4422 enh(File): Linux, macOS: microsecond precision for file times
  - PR #4390 enh(DateTimeParser): option to cleanup input string before parsing (#569)

!!Bug Fixes and Improvements

  - GH #4443 Upgrade libexpat to 2.6.0
  - GH #4425 Unit tests: optional testing of deprecated functionality
  - GH #4421 Multiple calls to initializeSSL/uninitializeSSL cause assert failure during certificate validation
  - GH #4411 NULL pointer: strategy when setting rotation never in FileChannel
  - GH #4404 qnx build error: 'prctl' was not declared in this scope
  - GH #4400 SocketReactor deadlock test intermittently hangs
  - GH #4398 Can not install CppUnit target
  - GH #4393 iOS ARM64 : Invalid access: Can not convert empty value.
  - GH #4392 Environment_WIN32U nodeIdImpl access violation in 1.13.0
  - GH #4375 UUID parser silently ignores too long strings
  - GH #4347 github check job on macOS: testEncryptDecryptGCM occasionally fails
  - GH #4313 Add macos sanitizer CI jobs
  - GH #4019 MSYS2/mingw cmake linking problem
  - GH #4018 cmake MSYS2 compile error for poco/net project
  - GH #3908 JWT token unitest fail with POCO_NO_SOO on vs 2019
  - GH #3650 MailMessage::read() chokes on "Content-disposition"
  - GH #3331 Apple Silicon ARM64 :  Invalid access: Can not convert empty value.
  - GH #3213 NetSSL_Win\src\SecureSocketImpl.cpp CertFreeCertificateContext on nullptr
  - GH #661 Automatic Lib Init (NetworkInitializer) is not working on MinGW
  - PR #4427 enh(tests): Ability to enable/disable testing of deprecated functionality
  - PR #4381 fix(Crypto): Compile error if EVP_CIPHER_CTX_init not defined.


!!!Release 1.13.0

!!Summary of Changes

  - Support for MongoDB 5.1 and newer
  - C++17 is the lowest supported standard
  - Poco::Data SQLParser (experimental, optional at build and runtime)

!!Breaking Changes

  - GH #4305 Remove deprecated `toJSON` functions
  - GH #4304 NumericString conversions inconsistencies
  - GH #4235 Minimum standards: C++17 and C11
  - GH #4230 Poco::Data fixes and improvements
  - GH #3701 SocketReactor: Remove not useful handlers calls
  - GH #569 SyntaxException for DateTimeParser::parse not working

!!Features and Enhancements

  - GH #4276 MongoDB default function deleted clang warning
  - GH #4261 Move autoCommit to AbstractSessionImpl
  - GH #4254 MessageHeader: provide original HTTP header values before RFC2047 decoding
  - GH #4249 Separate CI ODBC tests into separate job
  - GH #4217 Protect Reactor stop() and wakeUp() from reentrance
  - GH #4208 Add Unix socket support on windows
  - GH #4206 Improve Data::SessionPool thread safety
  - GH #4205 Data CI Improvements
  - GH #4198 Poco::Data fixes and improvements
  - GH #4183 Return Transaction execution status and error
  - GH #4181 Virtualize ServerApplication::handlePidFile()
  - GH #4160 Allow row count statements in Data::Recordset
  - GH #4148 SQL server stored procedures fail
  - GH #4146 ODBC max field size fails with int
  - GH #4129 make clean and distclean should not trigger dependencies creation
  - GH #4112 Redirect build stderr to a file
  - GH #4107 SQLChannel fixes and improvements
  - GH #4064 Add ProcessRunner and PIDFile
  - GH #4063 pthread_setname_np was not declared in this scope
  - GH #3951 Poco::Data::SessionPool: avoid sessions staying idle too long
  - GH #3833 DynamicStruct::toString() escaping
  - GH #3808 ICMPEventArgs Statistics bugs
  - GH #3740 buildwin.ps1 failed to build x64
  - GH #3713 SocketReactor improvements
  - GH #3710 Thread::trySleep() assertion
  - GH #3703 POSIX Thread::sleep() poor performance
  - GH #3702 SocketReactor: post ErrorNotification on exception
  - GH #3667 NumberFormatter: add Options enum for controlling prefix and lowercase
  - GH #2967 build Poco Net failed MinGW [TIMESTAMP_REQUEST enum vs macro]
  - GH #2770 Support for AF_UNIX on windows in recent windows builds.
  - GH #2707 Trying to Compile with emscripten: Target architecture was not detected as supported by Double-Conversion
  - GH #2578 HTTPClientSession not working with UNIX_LOCAL SocketAddress
  - GH #2403 File::exists() wrong result
  - GH #2331 Improve implementation of logging macros.
  - GH #2282 Add Path::self()
  - GH #1258 Poco::DateTimeParser::tryParse issue
  - GH #3845 Poco::XML::Node `insertAfter` API
  - GH #3659 Add thread name support
  - GH #2291 Visitor Pattern for Dynamic::Var
  - PR #4059 Update ICMPv4PacketImpl.h
  - PR #4021 Fix compile with `-DPOCO_NET_NO_IPv6`
  - PR #3885 Use map from key to count instead of multiset
  - PR #3864 Remove unnecessary dup. of std::string in NumberParser::tryParseFloat
  - PR #3802 ODBC: Fix DataFormatException getting Time value from SQL Server
  - PR #3797 HTTPServer Applications Slow to Terminate #3796
  - PR #3787 fix(Crypto) Update method to extract friendlyName from certificate
  - PR #3705 Fix/posix sleep
  - PR #3664 set thread name
  - PR #3657 Add lower case format for `NumberFormatter`
  - PR #4144 add visitor pattern implementation for Poco::Dynamic::Var
  - PR #3476 add separate accessors and mutators for connect, send and receive tim…

!!Bug Fixes and Improvements

  - GH #4328 Environment::nodeId Should Throw SystemException When Node ID is 0
  - GH #4311 Canceled `Task` shouldn't start running
  - GH #4310 `ActiveThread` data race
  - GH #4309 `ArchiveStrategy` data race
  - GH #4308 `DirectoryWatcher` data race
  - GH #4307 `NotificationCenter` data race
  - GH #4274 Remove VS 140, 150 Projects
  - GH #4259 Progen uses wrong AdditionalOptions separator
  - GH #4252 SecureSocketImpl::currentSession() always return null
  - GH #4244 Poco::Data::PostgreSQL::SessionHandle::setAutoCommit(bool) should not call commit() or startTransaction()
  - GH #4241 Poco::FileInputStream broken in 1.12.5 and 1.11.8.
  - GH #4231 Poco::Data::PostgreSQL::SessionHandle::disconnect() leaks memory for failed connections
  - GH #4207 VS170 binary names mismatch on ARM
  - GH #4187 Sync 1.11.-1.12-devel(1.13)
  - GH #4109 Skip reset for null Binder
  - GH #4106 ODBC Binder does not retrieve proper type precision
  - GH #4093 PostgreSQL get sqlcode
  - GH #4028 Incompatibility with gcc 13.1
  - GH #3923 UDP Multicast : `leaveGroup()` method always throw an exception
  - GH #3835 DynamicStruct::toString not wrapping empty strings
  - GH #3823 Possibility of memory leak in Windows Environment nodeIdImpl?
  - GH #3812 Poco::Data::Session::reconnect throw Connection in use
  - GH #3704 TaskManager waits for all threads in the ThreadPool
  - GH #3557 HTTPSClientSession read infinite loop on IOS
  - GH #3484 Poco::MongoDB support for MongoDB 5.0?
  - GH #3331 Apple Silicon ARM64 :  Invalid access: Can not convert empty value.
  - GH #3277 Use sendfile system call on Linux in HTTPServerResponseImpl::sendFile
  - GH #3165 Can't reuse Poco::Data::Statement with a new set of bindings
  - GH #2978 waitForTermination is unreliable on Linux.
  - GH #2976 SharedMemoryImpl x64 size error
  - GH #2965 Net fails to build with MinGW 9.20
  - GH #2634 Data race in Poco::Net::HTTPServerConnection::onServerStopped
  - GH #2366 Poco::Process::launch (UNIX) - possible memory leak when launching invalid command
  - GH #2332 Optimize multi-arg logger methods to only call format() if log level allows
  - PR #4353 Fix some issues found with clang-tidy
  - PR #4345 Build.macos.openssl@1.1
  - PR #4339 Fix RemoteSyslogChannel setProperty value check
  - PR #4333 enh(NumberFormatter): Introduce backward compatible options for formatHex functions
  - PR #4321 Github Action for Android NDK
  - PR #4319 Implementation of Path::self()
  - PR #4317 enh(ci): Add ENABLE_COMPILER_WARNINGS to cmake
  - PR #4306 3102 json lowercase hex
  - PR #4275 fix(NetSSL_Win): Error during handshake: failed to read data
  - PR #4270 SplitterChannel addChannel - Prevent Duplicate Channels
  - PR #4256 Implement MySQL::SessionHandle::startTransaction as submitting the SQL statement 'BEGIN'
  - PR #4223 Virtualize ServerApplication::handlePidFile()
  - PR #4211 Improve FifoEvent, ActiveMethod, ActiveResult
  - PR #4200 fixed infinite loops
  - PR #4199 fix(Poco::Data): fixes and improvements #4198
  - PR #4190 CMake: Use CMAKE_INSTALL_* variables from GNUInstallDirs
  - PR #4156 Allow passing raw fd's into ServerSocket
  - PR #4138 add missing check when activerecord is enabled
  - PR #4137 Fix platform when building for iPhoneSimulator
  - PR #4103 Fix openssl session resumption, FTPS certificate validation vs hostname
  - PR #4099 added new memeber SqlState to PostgreSQLException and made use of it.
  - PR #4068 AutoPtr: do 'duplicate' before 'release'
  - PR #4061 Adding API XML::AbstractContainerNode::insertAfterNP()
  - PR #4025 EVPPKey constructor for modulus/exponent
  - PR #4022 Make Binding and CopyBinding specializations final
  - PR #4020 MongoDB: add missing name accessor to get database name.
  - PR #4007 add sendfile method for streamsocket
  - PR #4004 Mongodb op msg database commands fix
  - PR #3989 Fix thread compilation issues on FreeBSD
  - PR #3976 fix(devel): add missing 1.11 releases commits
  - PR #3954 Complimentary to #3918 (std::*mutex wrapper)
  - PR #3946 Add GNU Hurd support
  - PR #3939 Solaris.build fix #3843 and #3643
  - PR #3932 Cross-compiling with ming32-w64 on Linux #3815
  - PR #3929 Fix multicast leave group
  - PR #3863 testDynamicStructEmptyString always failed
  - PR #3861 Do not incur insane stack limit in Foundation-ThreadPool test.
  - PR #3860 Fix Aix Build
  - PR #3842 hasMicrosecond is undefined
  - PR #3821 chore(Net): correct spelling, remove some unused codes fix(SocketProactor): missing adding sock to read pollset fix(DialogServer): _lastCommands data race
  - PR #3810 Custom rotate, archive and purge strategies for FileChannel
  - PR #3749 buildwin.ps1 script error building the x64 version of Poco
  - PR #3502 Add ODBC DirectExec public API
  - PR #3102 Made it possible to use lowercase hex numbers, also when encoding JSON
  - PR #3009 switching iPhoneSimulator arch to 64 bit


!!!Release 1.12.5p2

!! Summary of Changes

  - GH #4320: Integer overflow in Poco::UTF32Encoding


!!!Release 1.12.5p1

!! Summary of Changes

  - GH #4241: Poco::FileInputStream broken in 1.12.5 and 1.11.8


!!!Release 1.12.5

!!Summary of Changes

  - Merge changes from 1.11.5 to 1.11.8.
  - Upgrade double-conversion to 3.3.0
  - Upgrade bundled pcre2 to 10.42

- GH #4219 Make POSIX event thread safe
- GH #4215 Remove SocketReactor dependency on Poco::Thread for sleeping
- GH #4197 ODBC::Binder UUID new/free mismatch
- GH #4194 PollSet filters out some events
- GH #4189 Use after free warnings
- GH #4180 receiveResponse() may not return response body stream
- GH #4177 Upgrade bundled pcre2 to 10.42
- GH #4147 missing \r\n when setting trailer header in chunked response
- GH #4134 Initialisation of _socketIndex in SSLManager (OpenSSL)
- GH #3867 Add options to disable STDIO in child process
- GH #3832 pthread_getname_np' was not declared in this scope
- GH #3786 FileChannel::setRotation overflow
- GH #2776 Shutdown TLS1.3 connection
- GH #4176 PCRE2 10.40 version has security vulnerabilities（CVE-2022-41409）, when is the plan to fix it third-party
- GH #4150 Use Poco format instead of sprintf in Util
- GH #4116 Logging should evaluate only if the logging level is active
- GH #4071 PageCompiler: add referrerPolicy to page directive feature
- GH #4057 ODBC: SQL Anywhere Support
- GH #4031 Classes with virtual functions missing virtual destructors (compilation issues)
- GH #4023 CPPParser: Losing data if parameter std::function<void(bool)> is used
- GH #4014 wrong string offset in HTTPCredentials::isNTLMCredentials
- GH #4005 On UNIX platform, Poco::Path::getExtension() returns name of the hidden file if no extension is present
- GH #3986 Fix dead lock on Timer destructor
- GH #3968 Poco::Net::SocketConnector constructor should take SocketAddress by const reference
- GH #3935 The extractor in postgresql drops milliseconds
- GH #3926 CppParser throws exception when return value is specified to be in global namespace
- GH #3921 Deadlock in Timer when one sync and one async cancel requests are issued
- GH #3918 Static FastMutex fails to lock when issued from another thread on linux
- GH #3880 NetSSL_OpenSSL: Support session resumption with TLSv1.3
- GH #3876 Replace sprintf with snprintf in Environment and NumberFormatter to avoid deprecation warnings
- GH #3859 zlib headers not updated
- GH #3806 HTTPClientSession::receiveResponse() gives NoMessage instead of Timeout exception for SSL connection on Windows when using OpenSSL 3.0.x
- GH #3723 DateTimeFormatter creates invalid ISO8601 string
- GH #3147 Reading from request stream hangs when "Transfer-Encoding: chunked" is used
- GH #4218 Upgrade double-conversion to 3.3.0
- PR #4210 Fix pthread_setname not declared
- PR #4072 optimize checkUpperLimit and checkLowerLimit in VarHolder.h  enhancement
- PR #4050 rename arc -> poco_arc
- PR #4038 Fixed Poco::format specifier for error code  bug platform_specific
- PR #4011 fix #4005 Poco::Path::getExtension()
- PR #3999 Fix hang in destructor
- PR #3992 Fix thread counter leak
- PR #3987 Fix dead lock on Timer destructor
- PR #3971 Fix error handling with OpenSSL 3.0 in SecureSocketImpl.cpp (fixes #3806)
- PR #3943 Fix build for QNX  platform_specific
- PR #3942 Fix data race when create POSIX thread
- PR #3912 Fixed compile error for OpenSSL 1.0 systems (#3739)
- PR #3883 Added system_error header to SockerProactor for std::error_code
- PR #3855 Fix epollfd validity checks when compiling with wepoll
- PR #3809 improve Windows OpenSSL 3.0.x error handling #3806
- PR #3769 Fixed converting/correcting pre-gregorian dates (#3723)



!!!Release 1.12.4

!!Summary of Changes

  - GH #3849: Upgrade bundled libexpat to 2.5.0 [fixes CVE]
  - GH #3852: SocketReactor - 100 % CPU usage on single-core system


!!!Release 1.12.3

!!Summary of Changes

  - GH #3682: Including Any.h causes Compiler error on Visual Studio
  - GH #3683: Poco::format receives empty Vector<Any>
  - GH #3692: v1.12.0 and v1.12.1 Poco::Any compile problem
  - GH #3723: DateTimeFormatter creates invalid ISO8601 string
  - GH #3737: Poco EventImpl for Win32 breaks INFINITE wait
  - GH #3744: bindImplLOB zero size (or NULL) HY104 "Invalid precision value"
  - GH #3748: Poco 1.12.2 - Building DataMySQL still fails (issue #3689)
  - GH #3753: Fix optional JSON support for MySQL
  - GH #3763: Poco::Util::Application VxWorks main is incorrect
  - GH #3769: Fixed converting/correcting pre-gregorian dates (#3723).
  - GH #3795: aix system NumberFormatter.cpp build error
  - GH #3805: Upgrade expat to 2.4.9
  - GH #3814: Security Vulnerability identified in POCO version 1.12.2
  - GH #3825: VS 2022 Net project missing ARM64 config
  - GH #3834: SocketImpl::available()
  - GH #3846: Upgrade bundled zlib to 1.2.13 [fixes CVE]
  - GH #3847: Upgrade bundled SQLite to 3.39.4


!!!Release 1.12.2

!!Summary of Changes

  - GH #3460 fix(Platform): LoongArch support
  - GH #3688 Linking Crypto-testrunner fails
  - GH #3693 VS2022 compilation issues
  - GH #3698 SIGABRT in OSSL_PROVIDER_unload with static OpenSSL3
  - GH #3699 POSIX Event state need not be atomic
  - GH #3700 Release script must include build_vs170.cmd
  - GH #3708 wakeup fd is never read
  - GH #3711 lowercase winsock2 and iphlpapi to allow cross compile
  - GH #3716 Compiling with clang_cl_x64_x64 on Visual Studio 2022 failed
  - GH #3717 Only support extracting JSON fields when the SDK supports it
  - GH #3719 PocoNet Project File does not support Visual Studio 2022
  - GH #3720 Update max MSVC version information
  - GH #3721 fails to build with Visual Studio projects
  - GH #3722 Added missing Crypto Header to ProGen source
  - GH #3724 Crypto: Progen again to add new files and bring back ARM64 configuration
  - GH #3727 Add how to install using Conan section on README
  - GH #3733 Poco::format and C++20
  - GH #3734 g++ C++20 warnings


!!!Release 1.12.1

!!Summary of Changes

  - GH #3677 PocoFoundationConfig.cmake should now check for PCRE2
  - GH #3686 SIGSEGV in OSSL_PROVIDER_unload with static OpenSSL3


!!!Release 1.12.0

!!Summary of Changes

  - This release introduces Prometheus library
  - Upgraded bundled PCRE to PCRE2 10.40
  - Upgraded double-conversion to v3.2.0
  - Small object optimization for Any and Dynamic::Var (compile-time option, enabled by default)

  - GH #709: Poco::Dynamic::Var memory leak
  - GH #1039 Race condition in Poco::AsyncChannel close/log
  - GH #1459 Fix Poco::Net::Socket::select() epoll and poll implementations
  - GH #1687 SQLite Notifier has no table information
  - GH #1884 Poco::Net::TCPServerDispatcher::run() issue
  - GH #2084 LogFile_STD (LogFileImpl) fails to recover from getting out of space
  - GH #2085 Crash due to race condition in TCPServerDispatcher
  - GH #2091 Integrate windows poll
  - GH #2222 Warning when compiling my that's use VarHolder
  - GH #2270 Poco::Net::HTTPClientSession not supporting binding source address for proxy connect
  - GH #2271 Poco::Net::HTTPClientSession source IP address
  - GH #2285 Poco::Data::SQLite::Connector::open() crashes on db file with non existing directory
  - GH #2287 Poco::Data::Statement becomes unusable after exception
  - GH #2352 Allow setting the socket of SecureSocketImpl to no-blocking
  - GH #2386 As of C++11, std::swap is noexcept
  - GH #2401 Poco::Net::MailMessage::read hangs on missing final multipart boundary
  - GH #2457 Poco::Redis after executing "auth" command next command always return "OK"
  - GH #2465 Operating system specific thread ID is not available any more in Logger/Formatter
  - GH #2470 Can't use Poco::MongoDB::Cursor on aggregation cursor
  - GH #2511 Negative precision in NumberFormatter::format()
  - GH #2513 Poco::Net::SocketConnector unregistering
  - GH #2516 SHA3Engine hard to use with HMACEngine duplicate enhancement
  - GH #2521 Poco::Data::MySQL::Extractor null value extraction
  - GH #2538 Poco::Data::Session::connector() returns empty string for MySQL session
  - GH #2569 Poco::Data::MySQL timestamp
  - GH #2576 Add std::chrono support to Timespan
  - GH #2590 Poco::Zip 64-bit extensions not set
  - GH #2614 Poco::Net::NTPClient ignores second fractions enhancement
  - GH #2619 Decoding URI query parameters incompatible with Spring 5
  - GH #2638 Upgrade Windows SDK Used for Building Poco
  - GH #2688 Static code analyzer warnings
  - GH #2691 MinGW: fatal error: kernelspecs.h: No such file
  - GH #2706 [windows bug] pollset WSAPoll with non blocking socket will not report error
  - GH #2755 Poco::Data::MySQL LONGTEXT
  - GH #2821 Poco::Buffer: full on creation
  - GH #2849 setPadding does nothing when OpenSSL 1.1.x is used
  - GH #2864 SessionImpl begin
  - GH #2940 Add vcpkg installation instructions
  - GH #2943 Avoid clang 10 -Wimplicit-int-float-conversion warning/error when converting int into float
  - GH #2959 Fix percent-encoded fragment modification in Poco::URI
  - GH #2980 Memory leaks in Poco::Any
  - GH #2986 Once exhausted, Poco::ObjectPool does not lend out returned objects
  - GH #3016 Poco::MongoDB::Array interface improvements
  - GH #3026 Poco::Net::HTTPDigestCredentials added support for RFC7616 algorithms
  - GH #3039 Poco errors with _DEBUG and NDEBUG
  - GH #3052 Fix constness of URI::getPathSegments
  - GH #3088 Fix error in find_package example
  - GH #3056 Inconsistent behavior ConsoleChannel vs. WindowsConsoleChannel
  - GH #3062 Makefile: space(s) following target name will break build (during link)
  - GH #3073 libPocoCrypto.so: undefined reference to `pthread_atfork' when linking statically with OpenSSL 1.1
  - GH #3104 Publicly expose Poco::Message parameters
  - GH #3105 CMake: use GNUInstallDirs
  - GH #3175 SharedLibrary::isLoaded() not thread safe
  - GH #3195 MinGW also defines __GNUC__
  - GH #3240 Task::postNotification possible leak
  - GH #3241 Poco::Data::SessionPool change connection timeout
  - GH #3251 Poco::JSON Serializing NAN
  - GH #3253 Arbitrary indent of 2 applied to JSON string objects Var conversion
  - GH #3261 Upgrade to PCRE2 latest version
  - GH #3283 DatagramSocket does not allow IPV6_V6ONLY
  - GH #3296 Add move semantics to Net (sockets and addresses)
  - GH #3297 Poco::Foundation Fails to Compile With POCO_ENABLE_SOO Defined
  - GH #3323 Extend format patterns %T and %I to support native threads
  - GH #3342 DB into() does not compile for more than 20 members in a tuple
  - GH #3357 Add socket proactor
  - GH #3359 Make PollSet::poll() interruptible
  - GH #3371 SocketReactor::getNotifier() does not use socket.impl()
  - GH #3372 FTPClientSession::activeDataConnection 1.11.0 cannot set specific data port
  - GH #3374 No access to padding in Cipher
  - GH #3375 Poco::Net::PollSet::SocketModeMap poll(const Poco::Timespan& timeout) hasSignaledFDs issue slow down connections
  - GH #3378 Poco::Net::PollSet function setMode 1.11.0 cause while(1) on windows
  - GH #3380 Windows SO_REUSEADDR is neither reliable nor safe
  - GH #3384 Always set thread names on POSIX platforms
  - GH #3385 Poco::Net::IPAddress::tryParse does not work for "::"
  - GH #3396 Poco::Data::ODBC - dbEncoding property not used for insert/update
  - GH #3399 IPAddress::isLoopback() returns false for IPv4 mapped in IPv6
  - GH #3404 Poco::Net: make MessageHeader limits configurable
  - GH #3415 OpenSSL 3.0 support
  - GH #3417 Calling SocketReactor's run() method in a program's main thread yields a lot of null pointer exceptions
  - GH #3421 Cannot use HMACEngine with SHA2Engine
  - GH #3452 Syslog: Include Facility to Syslog Message
  - GH #3453 added facility to SyslogChannel
  - GH #3460 LoongArch support
  - GH #3481 Poco::JSON DataType support for MySQL
  - GH #3482 Visual Studio 2022 (v170) missing from buildwin.cmd
  - GH #3486 Windows on ARM64 support
  - GH #3516 Fix OpenSSL 3.0 deprecated warnings
  - GH #3529 Added LocalConfigurationView to only search inside the viewed scope
  - GH #3543 Visual Studio Warning C4244
  - GH #3558 Race condition in SSLManager
  - GH #3561 Add envelope to crypto
  - GH #3569 Readded named substring support for regexes
  - GH #3580 Rounds very large negative numbers to the incorrect values
  - GH #3592 Add 425 / HTTP_TOO_EARLY to HTTPResponse::HTTPStatus
  - GH #3598 Poco::Net::Socket::available does not always return correct value for UDP
  - GH #3602 Add Poco::Data::JSONRowFormatter
  - GH #3603 Update minimum GCC version information
  - GH #3611 VS2022 Arm64 projects missing or do not load
  - GH #3613 Poco::Net::UDPHandler data race
  - GH #3620 MariaDB still uses tx_isolation for transaction isolation unlike MySQL 8+ which uses transaction_isolation
  - GH #3624 Upgrade double-conversion to v3.2.0
  - GH #3628 Poco::Net::PollSet data race
  - GH #3629 Event data race
  - GH #3633 Poco::Redis: Support Authentication
  - GH #3635 ConfigurationView and JSON is broken for array access
  - GH #3639 Bugcheck: indicate compiler that functions will never return
  - GH #3640 fix warning C4717: 'format<vector<any>': recursive on all control paths, function will cause runtime stack overflow
  - GH #3641 FifoBuffer.advance method not throw exception when length==0
  - GH #3642 Make ParallelSocketReactor thread namable
  - GH #3651 Poco::Net::TCPserver missing from Net/samples/CMakeLists.txt
  - GH #3652 Linking with Foundation on Android gives error
  - GH #3655 Poco::Net::Socket::select EPOLL implementation returns socket in exceptList when empty list is given
  - GH #3658 Support for chunked transfer encoding trailer
  - GH #3661 Poco::Net::PollSet::add()/update() semantics
  - GH #3665 MSVC does not properly recognize std version

!!Incompatible Changes and Possible Transition Issues

  - All swap operations are noexcept now
  - PollSet::add() is mode-cumulative now
  - UDPServer now requires explicit starting
  - Move semantics for sockets and SocketAddress (compile-time option, disabled by default)


!!!Release 1.11.8

!!Summary of Changes

  - GH #1372: Possible deadlock in SessionPool
  - GH #4170: Poco::FileStream is always opened with std::ios::in | std::ios::out bug
  - GH #4169: Upgrade bundled zlib to 1.3.
  - GH #4171: Upgrade bundled sqlite to 3.43.1
  - GH #4162: [Bug] class KeylessActiveRecord is missing export macro
  - GH #4164: [Bug] SharedLibraryImpl::loadImpl uses an incorrect format specifier
  - GH #4173: AbstractConfiguration: when expanding property references, allow specifying a default value
  - GH #4174: AbstractConfiguration: support Int16/UInt16 and Int32/UInt32
  - GH #4182: Util: Make load()/save()/clear() operations on configurations thread-safe
  - GH #4184: Poco::TemporaryFile: make filenames less predictable
  - GH #4195: Poco::File::created() on macOS should use birthtime


!!!Release 1.11.7

!!Summary of Changes

  - GH #4023: CPPParser: Losing data if parameter std::function<void(bool)> is used
  - GH #4031: Classes with virtual functions missing virtual destructors (compilation issues)
  - GH #4014: wrong string offset in HTTPCredentials::isNTLMCredentials
  - GH #4071: PageCompiler: add referrerPolicy to page directive
  - GH #4077: OpenSSL 3: Don't throw if legacy provider is not available
  - GH #4078: Upgrade bundled SQLite to 3.42
  - GH #4079: SocketConnector: constructor should take SocketAddress by const reference
  - GH #4082: Logger performance improvement with formatting


!!!Release 1.11.6

!!Summary of Changes

  - GH #3147: Reading from request stream hangs when "Transfer-Encoding: chunked" is used
  - GH #3859: zlib headers not updated
  - Build system fixes for Xcode on Apple Silicon.
  - Upgrade bundled SQLite to 3.40.0


!!!Release 1.11.5

!!Summary of Changes

  - GH #3849: Upgrade bundled libexpat to 2.5.0 [fixes CVE]


!!!Release 1.11.4

!!Summary of Changes

  - GH #3805: Upgrade expat to 2.4.9
  - GH #3846: Upgrade bundled zlib to 1.2.13 [fixes CVE]
  - GH #3847: Upgrade bundled SQLite to 3.39.4


!!!Release 1.11.3

!!Summary of Changes

  - GH #3567: fix(openssl-initializer): check legacy provider existence for legacy exception
  - GH #3587: MySQL UUID binding temporary string
  - GH #3632: Redis - add TLS support
  - updated a few copyright dates


!!!Release 1.11.2

!!Summary of Changes

  - GH #2882: Handle negative DST offset
  - GH #3268: Poco redis command set have a bug when you want to set nx ex or expireTime
  - GH #3338: NamedMutex does not work on Linux distributions where fs.protected_regular=1
  - GH #3448: Basic support for OpenSSL 3.0.0
  - GH #3458: encryptString() crash on redhat/centos 8 with FIPS enabled using md5 default digest
  - GH #3465: NetSSL_Win: bad error handling when decodeMessage() fails
  - GH #3466: DefinePlatformSpecific.cmake: handle RelWithDebInfo and MinSizeRel configurations
  - GH #3470: bug in JSON ParseHandler.cpp (RFC 7159 should be valid)
  - GH #3472: Add Windows 11 detection to Environment_WIN32U
  - GH #3483: Adds Windows 11 and Server 2022 to Environment::osDisplayName()
  - GH #3485: Adds Visual Studio 2022 (v170) to buildwin.cmd
  - GH #3495: Should the Array::operator[] throw?
  - GH #3500: Sandbox all iFrames in PocoDoc
  - GH #3504: OpenSSL 3 compatibility
  - GH #3505: JSON::PrintHandler.value(bool) prints incorrect value
  - GH #3507: Reference counting for bound configuration in Util::Option is broken
  - GH #3508: #3507: Fix bound configuration reference counting in Poco::Util::Option.
  - GH #3509: fix dst and utcOffset handling for Dublin time zone
  - GH #3515: NetSSL_OpenSSL Testsuite: testInterop() and testProxy() fail due to changed certificate
  - GH #3518: Expat version check in #defines incorrect.
  - GH #3519: Add convertation to string in Redis Command#set
  - GH #3524: [linux] Why is Poco::XML linked to Poco::Zip target?
  - GH #3525: Bad management of file in case of OpenSSLException in X509Certificate::readPEM and X509Certificate::writePEM
  - GH #3538: Upgrade bundled expat to 2.4.7
  - GH #3544: Add back NIOS2 double conversion detection to fix compile errors
  - GH #3549: Test against the correct signatures in the JWT ES384 and ES512 tests
  - GH #3553: Upgrade bundled zlib to 1.2.12
  - GH #3559: Poco::Data::PostgreSQL - DateTime extraction truncates fractional seconds
  - GH #3563: Remove support for OpenSSL < 1.0


!!!Release 1.11.1

!!Summary of Changes

  - Upgraded bundled PCRE to 8.45
  - Upgraded bundled SQLite to 3.36.0
  - GH #2823: error: implicit conversion from 'int' to 'float' changes value from 2147483647 to 2147483648
  - GH #2966: SocketReactor loads one core of CPU up to 100%
  - GH #3221: Crash reported on Windows in X509Certificate verification
  - GH #3330: Poco::Data::ODBC::ODBCStatementImpl causes crash
  - GH #3334: Fork error on tvOS and watchOS
  - GH #3335: XML error when build 1.11.0
  - GH #3344: [bug] MacOS bundle destination path is not set
  - GH #3345: fix cmake bundle
  - GH #3347: The definition POCO_NO_FORK_EXEC is not respected or something like this
  - GH #3353: fix fork option
  - GH #3360: Add POCO_PGSQL_{INCLUDE,LIB} variables
  - GH #3363: Fixed compilation error with MongoDB::Connection and Util::Application
  - GH #3377: Updates comments for windows
  - GH #3381: DNS::hostByAddress not thread-safe
  - GH #3397: Fix crash due to X.509 certificates with Subject Alternative Name other than DNS Host
  - GH #3400: fix std::localtime not thread safe
  - GH #3414: fix missing expat definition
  - GH #3425: Fixed suspend/resumeEvents pair in DirectoryWatcher


!!!Release 1.11.0

!!Summary of Changes

  - This release introduces ActiveRecord, a simple and lightweight object-relational mapping
    (ORM) framework based on the Active Record pattern and the Data library.
  - Upgraded bundled expat to 2.4.1
  - Upgraded bundled PCRE to 8.44
  - Upgraded bundled pdjson to latest master
  - Upgraded bundled SQLite to 3.35.5
  - GH #2205: Start POCO as Windows service with parameters fix #2190
  - GH #2418: SecureServerSocket doesn't work with IpV6
  - GH #2677: Fix CLOB type support in StatementImpl and RecordSet
  - GH #2746: Race in TCPServerDispatcher::stop
  - GH #2783: Invalid condition [ICMPv4PacketImpl.cpp:234]
  - GH #2825: riscv: Enable double operations when using double float abi
  - GH #2895: Settings to verify OCSP stapling response (if received any) for client connections
  - GH #2904: Support environments without hardware floating point
  - GH #2906: Support environments without hardware floating point
  - GH #2927: Fix assigned value to wrong pointer
  - GH #2928: Fix clang issue
  - GH #2929: Zip and SevenZip do not depend on Util, XML, JSON
  - GH #2932: Poco::Net::NTLMContext is missing dllexport/dllimport symbols
  - GH #2935: Configuration to receive OCSP stapling response for client connection…
  - GH #2942: Avoid clang 10 -Wimplicit-int-float-conversion warning/error when con…
  - GH #2945: Iterating over Var containing empty container throws "Out of range" exception
  - GH #2970: Poco::Data::TypeHandler<Poco::Nullable<T>>::prepare() must prepare with underlying type, not Poco::Data::Keywords::null
  - GH #2982: Poco::Net - return value from close needs to be checked in SocketImpl
  - GH #2984: Fixed linking with Data ODBC error on some platforms
  - GH #2989: setting priorityNames property on PatternFormatter has no effect
  - GH #2992: CryptoTransformImpl::setPadding(int padding) incorrect for OpenSSL >= 1.1
  - GH #2993: The Sec-WebSocket-Key of WebSocket is always the same one
  - GH #3019: ObjectPool wait on borrow condition fix
  - GH #3021: PatternFormatter priorityNames fix
  - GH #3022: Process::isRunning(PID pid) causes handle leak on Windows
  - GH #3023: Link to "discussion forums" on "How to get help" advice broken
  - GH #3025: PKCS12Container: fix memory leaks on exceptions (1.10.1)
  - GH #3027: PKCS12Container: fix memory leaks on exceptions (devel)
  - GH #3037: Poco::toJSON: Don't escape forward slash in JSON strings
  - GH #3041: PostgreSQL and TEXT column type
  - GH #3044: Upgrading PCRE to 8.44 is it in immediate plan?
  - GH #3045: PostgreSQL and BYTEA column type
  - GH #3057: Poco::Data::PostgreSQL::SessionImpl::connectorName() returns empty string
  - GH #3059: MessageHeader::splitParameters fails on 'httponly' cookie with 'string too long' exception
  - GH #3061: SocketImpl::bind --> bind wrong config
  - GH #3064: PostgreSQL: Extraction of 16-bit integers corrupts result
  - GH #3066: CMake warning about MYSQL casing
  - GH #3067: Fix pkg-name in find_package_handle_standard_args
  - GH #3068: Documented ENABLE_JWT option
  - GH #3074: Fix sessions may not return back to the pool
  - GH #3076: Avoid access to already freed memory in JSON code
  - GH #3078: Fix typo in the ThreadPool's docs
  - GH #3086: Use POCO_IOS_INIT_HACK for Linux in combination with libc++
  - GH #3089: HTTPSessionFactory does not support HTTPClientSession::ProxyConfig
  - GH #3090: Do not initialize value with undefined behavior
  - GH #3091: feat(SharedLibrary): add more detailed error description when LoadLib…
  - GH #3092: feat(SharedLibrary): add more detailed error description when LoadLib…
  - GH #3095: Digest proxy authentication does not work in 1.10.1
  - GH #3097: Support for building Arm64 Apple Silicon
  - GH #3099: Fixed Postgres extraction into Dynamic::Var
  - GH #3107: unused variable
  - GH #3114: Added JSON Array::empty() method
  - GH #3116: Changed EventHandlerMap key
  - GH #3130: Possible use-after-free bug on the method copyToImpl
  - GH #3133: POCO_STATIC has been deprecated
  - GH #3135: Poco::Data::SQLite::Utility::fileToMemory unsuccessful if journal exists
  - GH #3136: Fixed null character issue when parsing a JSON
  - GH #3138: Add support of arch riscv32
  - GH #3141: allow to handle hot-journal (fixes #3135)
  - GH #3151: fix(JSON::Object): crash when a key is removed from object with JSON_PRESERVE_KEY_ORDER
  - GH #3153: Poco::Data::ODBC [N]VARCHAR(MAX) wrong maxDataSize() result
  - GH #3155: Fixed typo in overridden
  - GH #3157: fix(openssl): add missing dependency to OpenSSL components
  - GH #3159: Bug in NumericString with decSep != '.'
  - GH #3163: Correct Var::parse null value
  - GH #3166: Fix PostgresSQL BLOB extractor
  - GH #3168: Reference documentation contains invalid links.
  - GH #3169: #2746: Fix race condition on TCPServerDispatcher stop
  - GH #3182: Poco::Process:launch on MacOS BigSur
  - GH #3183: fix setPadding
  - GH #3190: [NetSSL_Win]: SSL-connection fails with "Host name verification failed error" (Regression bug)
  - GH #3191: Fixing a bug in the NetSSL_Win module (Host name verification failed error)
  - GH #3193: ServerApplication::registerService() unquoted path security vulnerability
  - GH #3196: std::forward for Poco::Optional ctor with rvalue
  - GH #3202: JWT: ESxxx signature must include padding for ECDSA R and S values
  - GH #3204: CryptoTransformImpl::setPadding wrong call
  - GH #3215: XML parser returns item from different element in a array
  - GH #3217: CMake: warning message with -DPOCO_STATIC confusing
  - GH #3219: SMTPClientSession: invalid SMTP command if empty recipients list in MailMessage
  - GH #3223: Compilation failure since OpenSSL (alpha 13)
  - GH #3224: Remove SSL23 support from Poco/Crypto
  - GH #3229: Upgrade bundled expat to 2.3.0
  - GH #3230: ECDSADigestEngine: include missing header
  - GH #3233: Feat/hash range
  - GH #3237: An error in the documentation for Poco/JSON/Parser.h
  - GH #3239: XML parser returns item from different element in a array #3215
  - GH #3242: RemoteSyslogListener: add reusePort option
  - GH #3245: find_package(Poco REQUIRED COMPONENTS ... NetSSL) requires an aditional find_package(OpenSSL) since poco-10
  - GH #3248: PollSet not working as intended on Windows
  - GH #3249: PollSet - poll() timeout not properly used
  - GH #3250: fix(PollSet): #3248 #3249
  - GH #3260: Memory leak in EVPPKey::loadKey used with files & wrong password
  - GH #3266: Order of Util::Application::uninitialize() is not in reverse as documented
  - GH #3269: Poco::Net::Context initialization with empty certificateFile
  - GH #3274: Fix localtime_r for VxWorks 6.9 and later
  - GH #3278: Fixing no hardware floating point support - Part II
  - GH #3279: Update bundled expat to 2.4.0
  - GH #3282: Update constant in setContentType documentation
  - GH #3284: JSON Fuzzing: Undefined-shift in poco_double_conversion::DiyFpStrtod
  - GH #3285: JSON Fuzzing: Stack-overflow in Poco::JSON::ParserImpl::handle
  - GH #3291: JSON Fuzzing: Stack-overflow with empty stacktrace
  - GH #3292: JSON Fuzzing: Stack-overflow in Poco::JSON::Array::~Array
  - GH #3295: A variation on Issue 949 comes back to life?
  - GH #3299: NetSSL: Allow per-Context InvalidCertificateHandler
  - GH #3301: Unterminated string possible  in NetworkInterfaceImpl::setPhyParams()
  - GH #3302: MSVC: Poco hides warnings (C4996) for the C++14 attribute [[deprecated]]
  - GH #3303: DNS HostEntry returns multiple entries
  - GH #3307: Poco::Crypto::X509Certificate: obtain certificate fingerprint
  - GH #3309: JSON parser copies entire JSON document to memory when parsing from stream.
  - GH #3310: Upgrade bundled SQLite to 3.35.5.
  - GH #3313: Upgrade bundled double-conversion
  - GH #3314: NetSSL_OpenSSL: any.pem certificate error: ca md too weak
  - GH #3315: Unintended sign/type conversion out of RecordSet
  - GH #3317: Data::MySQL MySQL headers and library search paths
  - GH #3318: Data: Support Poco::UUID for data binding
  - GH #3321: Feat/data db encoding
  - GH #3322: why is  useless MyStruct present in Thread_posix.cpp :: ThreadImpl::setPriorityImpl (easy to fix I guess)
  - GH #3326: [asan] Undefined behavior in ICMPv4PacketImpl.cpp


!!!Release 1.10.1

!!Summary of Changes

  - Upgraded bundled SQLite to version 3.31.1.
  - GH #2894: Poco 1.10.0 doesn't build with cmake & POCO_UNBUNDLED
  - GH #2898: poco 1.10/ NetSSL / openssl < 1.1 : default server usage changed (compare to 1.9.4)
  - GH #2834: Wrong cancelation of the fix: incorrect type of store name parameter in
    CertOpenStore API call into NetSSL_Win. Release 1.10.0
  - GH #2791: allow pre-allocation of the buffer in Poco::LogStreamBuf.
  - GH #2816: Modernise TLS configuration
  - GH #2818: Add getSpecifiedPort() method in Poco::URI.
  - GH #2909: Test failures on s390x architecture with 1.10.0
  - GH #2911: Poco::UTF16Encoding and Poco::UTF32Encoding byte order conversion bug
  - GH #2912: Poco::SHA2Engine computes incorrect hash on big-endian systems
  - GH #2923: cmake: Version 1.10.0 not parsed correctly from VERSION file
  - GH #2908: [Windows] Process arguments quoting is broken.
  - GH #2894: Poco 1.10.0 doesn't build with cmake & POCO_UNBUNDLED
  - GH #2920: Close Service Handle after DeleteService Function call
  - GH #2919: Fixed Crash in WinService::setFailureActions
  - GH #2922: 1.10 cmake build fails on FreeBSD 11.2 Release
  - MySQL: resetting the session when putting it back into a SessionPool is now optional
    (and disabled by default) due to a bug in MySQL messing up the character encoding when doing so.
  - Poco::AutoPtr and Poco::SharedPtr now support comparison with nullptr.


!!!Release 1.10.0

!!Summary of Changes

  - This release now requires a C++14 compiler (GCC 5, Clang 3.4, Visual C++ 2015).
  - Visual Studio project and solution files for versions prior to 2015 have
    been removed. Furthermore, the separate projects and solutions for 64-bit builds
    have been removed and configurations have been merged in a single project file.
  - POCO's fixed-size integer types are now based on <cstdint> types. This changes
    the definition of Poco::Int64 and Poco::UInt64 on some platforms.
  - Many methods exposing raw pointers have been changed to use smart pointers
    (usually Poco::SharedPtr or Poco::AutoPtr) instead. This may break some existing
    code. Specifically, the Logging framework in the Foundation library and the
    Configuration framework in the Util library have been changed.
  - New JWT library for dealing with JSON Web Tokens.
  - Upgrade bundled SQLite to version 3.31.0.
  - The NetSSL_OpenSSL library supports TLS 1.3 with OpenSSL 1.1.1 or later.
  - The NetSSL_Win library supports TLS 1.3 if it's supported by the underlying SChannel
    implementation.
  - Added support for NTLM authentication in the Net library.
  - NetSSL_OpenSSL now has a Poco::Net::FTPSClientSession and Poco::Net::FTPSStreamOpener
    class for connecting to FTP servers over TLS.
  - Fixed a potential crash in Poco::Net::NetworkInterface on Linux and macOS due to
    an invalid cast when obtaining the MAC address of an interface.
  - GH #2624: Poco::FileChannel/Poco:LogFileImpl::writeImpl() on Windows should translate \n to \r\n.
  - GH #2869: X509Certificate does not render UTF-8 characters in subjectName
  - GH #2863: NetworkInterface::map can fail to reveal some interfaces if an earlier one can not be handled
  - GH #2807: Poco::Data::ODBC Binding of SQL Decimal Type
  - GH #2812: String trimInPlace crashes with 0 size on Visual Studio Express 2017
  - GH #2830: Fix wrong buffer size in client handshake when re-using a SecureSocket [NetSSL_Win]
  - GH #2809: Allow to filter long tests using a command line argument
  - GH #2853: Poco::Process::launch process environment unicode support is broken on Windows
  - GH #2843: Poco::Net::MediaType::parse() does not split parameters
  - GH #2772: On iOS real device (not simulator) the home directory is not usable
  - GH #2689: Added tryWait() into Process and ProcessHandle. Handle kill()-ed UNIX process exit codes.
  - GH #2866: unescape Backslash char in UTF8 unescape method
  - GH #2879: Add support for SameSite attribute in HTTPCookie
  - GH #2824: Poco::Environment missing UTF8/wstring support on Windows
  - GH #2295: setEscapeUnicode() functions in JSON Array and Object classes ignore their boolean parameter
  - GH #2306: Why does Poco explicitly define _WIN32_WINNT?
  - GH #2802: Deprecated warning when building POCO with OpenSSL in submodule + cmake
  - GH #2884: Is it a description error about setReceiveTimeout()?
  - GH #2780: Allow Poco::Net::Context::usePrivateKey to accept ECKey and/or general EVPPKey
  - GH #2747: NetSSL_Win: Context constructor usage argument should specify minimum supported SSL/TLS version
  - GH #2745: Small problem in the code
  - GH #2743: X509Certificate validFrom expiresOn Date parsing
  - GH #2744: Poco::Mysql does not build with MySQL 8.0+
  - GH #2686: Uploads larger than 2GB fail
  - GH #2217: UUIDGenerator should allow random seed
  - GH #1609: Improve XDG Base Directory Specification implementation
  - GH #561: Support for XDG Base Directory Specification
  - GH #2881: Add an option to force the use of PollingDirectoryWatcherStrategy
  - GH #2584: Adding standard macOS legacy encodings
  - GH #2885: fix Dynamic::Var parse string issue
  - GH #2616: Restore pre-1.8.0 behaviour of Poco::Net::ServerSocket::bind.
  - GH #2641: Implement DataURIStream for extracting data from data URIs.
  - GH #2842: File fail on overwrite
  - GH #2840: Deleting Registry Keys on the WOW6432Node is not possible on 64Bit Applications
  - GH #2841: Service manager improvments
  - GH #2827: X509Certificate: Get rid of deprecated OpenSSL 1.1 APIs
  - GH #2826: CipherImpl: Fix small error with OpenSSL 1.1
  - GH #2775: Fix issue in NetSSL_Win. Windows Server 2016 reboots while trying to establish an SSL connection.
  - GH #2773: Fix the issue with incorrect type of store name parameter in CertOpenStore API call into NetSSL_Win
  - GH #2766: Support qnx sdp7
  - GH #2308: SocketAcceptor::setReactor() is broken
  - GH #2250: Poco::strToInt<> fails for values right above the type's boundary / limit
  - GH #2249: Poco::JSON::Object::set() should return reference to this to allow chaining.
  - GH #2275: SQLite mismatch open/close API calls
  - GH #1921: ICMPSocket does not check reply address
  - GH #2092: Use PollSet in SocketReactor
  - GH #2552: Poco::MongoDB test cases failed in Linux on IBM z
  - GH #2546: MySQL session state is not cleared in SessionPool
  - GH #2410: Preserve entries order in DynamicStruct
  - GH #2467: Can not open certain zip files include data descriptor
  - GH #2398: Poco 1.9.1 branch cmake build on FreeBSD 11.1 failed
  - GH #2365: add struct tm support to DateTime
  - GH #2348: NTPClient not checking reply address
  - GH #2346: lock-order-inversion in SocketReactor
  - GH #2330: add socket gather/scatter capabilities
  - GH #2343: UDPServer and client
  - GH #2329: add PMTU discovery
  - GH #2345: SocketNotifier not thread-safe
  - GH #2323: WebSocketTest.cpp faults reported by valgrind
  - GH #1160: Poco::Net::NetException "SSL Exception: error:1409F07F:SSL routines:ssl3_write_pending:bad write retry"
  - GH #2547: Reset connection when a session is returned to the SessionPool
  - GH #2451: http client timeout on Windows 7 and Server 2008 R2
  - GH #2417: Added missing IPv6 methods to SecureSocketImpl
  - GH #2408: add ordered containers
  - GH #2042: Android abstract namespace local socket address
  - GH #2088: Fix race condition in TCPServerDispatcher.cpp
  - GH #2892: SocketImpl::bind --> bind wrong config

!!Incompatible Changes and Possible Transition Issues

  - This release now requires a C++14 compiler (GCC 5, Clang 3.4, Visual C++ 2015).
  - POCO's fixed-size integer types are now based on <cstdint> types. This changes
    the definition of Poco::Int64 and Poco::UInt64 on some platforms.
  - Many methods exposing raw pointers have been changed to use smart pointers
    (usually Poco::SharedPtr or Poco::AutoPtr) instead. This may break some existing
    code. Specifically, the Logging framework in the Foundation library and the
    Configuration framework in the Util library have been changed.


!!!Release 1.9.4

!!Summary of Changes

  - fixed GH #2784: Upgrade bundled expat XML parser library to release 2.2.8,
    which fixes CVE-2019-15903.


!!!Release 1.9.3

!!Summary of Changes

  - fixed GH #2603: Remove incorrect upper size limits for SSL certificates in NetSSL_Win
  - fixed GH #2661: Poco::Zip::ZipArchive cannot load new tomcat.zip file (additional fix)
  - fixed GH #2742: Support of vs150 & vs160 with the official Microsoft localization executable,
    vswhere.exe, installed by MSVC starting from VS2017
  - Data/ODBC: make binding of std::string configurable (SQL_LONGVARCHAR - default or SQL_VARCHAR)
    through a global setting (Poco::Data::ODBC::Connector::bindStringToLongVarChar()).
  - added Poco::SharedLibrary::setSearchPath() (currently implemented on Windows only)
  - Windows required minimum version is now Windows XP SP2
  - upgraded bundled SQLite to 3.29.0
  - CppParser now supports type aliases defined with using keyword.
  - PageCompiler: added support for adding Content-Security-Policy and Cache-Control headers.


!!!Release 1.9.2

!!Summary of Changes

  - fixed GH #2736: Error using CMake gui - latest version
  - fixed GH #2737: Bundles vulnerable copy of Expat — please upgrade to Expat 2.2.7
  - fixed GH #2738: Poco::AccessExpireStrategy::onGet() must not extend expiration time after expiration


!!!Release 1.9.1

!!Summary of Changes

  - Added support for building with different OpenSSL distributions on Windows.
    See the POCO_EXTERNAL_OPENSSL macro defined in Foundation/include/Poco/Config.h
    for options.
  - Added Poco::Net::HTTPClientSession::flushRequest()
  - Added Poco::Net::WebSocket::setMaxPayloadSize() and Poco::Net::WebSocket::getMaxPayloadSize()
    to specify a maximum acceptable payload size for Poco::Net::WebSocket::receiveFrame().
  - Poco::Net::WebSocket: don't attempt to send empty credentials in response to 401 response.
  - Redis: added support for additional commands (exists, expire, ping, multi, exec, discard)
  - Redis: added Poco::Redis::Client::isConnected()
  - Upgraded bundled PCRE to version 8.43
  - Upgraded bundled SQLite to version 3.28.0
  - Added project/solution files for Visual Studio 2019
  - Fixed Visual Studio project files (version information from DLLVersion.rc not included in DLLs)
  - Include version resource in DLLs built with CMake
  - Added HTTP*Credentials::empty() and HTTPCredentials::clear()
  - fixed GH #2220: Encoding/DoubleByteEncoding.cpp fails to compile with VS2008 and _DEBUG
  - fixed GH #2243: DLLVersion.rc is excluded from build, missing detail information in properties of *.dll
  - fixed GH #2277: SQLite null pointer dereference occurs when exception is being thrown
  - fixed GH #2313: PollSet behaves differently on windows
  - fixed GH #2316: cmake can't find MySQL and ODBC libraries
  - fixed GH #2336: Omit ContentLength in WebSocket accept response
  - fixed GH #2358: Don't include <openssl/fips.h> for later OpenSSL
  - fixed GH #2364: Stringify escapes every unicode symbol when object contain an array
  - fixed GH #2380: Calling Poco::Net::X509Certificate::addChainCertificate() leads to double free.
  - fixed GH #2492: Net::Socket::address() crash on Android
  - fixed GH #2549: Fix keepAlive in http client session
  - fixed GH #2565: HTMLForm: optional enforcement of Content-Length instead of Chunked Transfer-Encoding
  - fixed GH #2570: DialogSocket: receiveStatusMessage() - line length limit applies to entire multi-line message
  - fixed GH #2583: Crypto library does not build with OpenSSL 1.0.0
  - fixed GH #2655: MongoDB Binary element to string - bug
  - fixed GH #2661: Poco::Zip::ZipArchive cannot load new tomcat.zip file
  - fixed GH #2700: Invalid read of memory in Poco::Environment::set which may cause crashes.
  - fixed GH #2712: File_WIN32.cpp(168): error C2065: “_upath”:Undeclared identifier
  - fixed GH #2723: Access violation when trying to decompress .zip file with unsupported compression method.


!!!Release 1.9.0

!!Summary of Changes

  - Added additional text encodings, available in the new PocoEncodings library (GH #2165)
  - Added Punycode support for resolving Internationalized Domain Names to Poco::Net::DNS (GH #2164)
  - Added XDG Base Directory Specification support in Poco::Path, Poco::Util::SystemConfiguration
    and Poco::Util::Application (GH #1609, GH #561, GH #1609)
  - Added support for GCM ciphers in Crypto library (GH #2129)
  - Poco::Net::RemoteSyslogChannel and Poco::Net::RemoteSyslogListener now have basic
    support for RFC 5424 structured data (GH #2173)
  - Poco::File now has methods for obtaining partition space (GH #1545)
  - Added Poco::Net::Context::addCertificateAuthority() (GH #2197)
  - Added Poco::AutoPtr::reset() and Poco::SharedPtr::reset() to improve compatibility
    with std::shared_ptr() (GH #2172)
  - fixed GH #703: Poco::Data::SQLite data types (INTEGER is now mapped to Int64)
  - fixed GH #1426: inttypes.h is available on sun solaris
  - fixed GH #1912: Run ping with custom data size #1912
  - fixed GH #2177: Run ping with custom timeout instead of the default
  - fixed GH #2058 and GH #2095: Synchronization issue/deadlock in Poco::Util::Timer at destruction
  - fixed GH #2089: Allow hyphen in HTTP authentication scheme names
  - fixed GH #2106: Undefined behavior in Delegate::equals()
  - fixed GH #2108: POCO SQLite Data Connector hangs for 20 secs waiting for thread timeouts on Windows
  - fixed GH #2142: JSON::Object preserveOrder keys not synced on assignment
  - fixed GH #2199 and GH #2188: Illegal header in zip file exception/assertion failure during JAR file decompression
  - fixed GH #2203: Use MAX_ADDRESS_LENGTH to determine buffer size


!!!Release 1.8.1

!!Summary of Changes

  - Added Poco::File::linkTo()
  - fixed GH #2044: Poco::Net::NetworkInterface::list does not list inactive interfaces
    even when explicitly being asked for it.
  - fixed GH #2042: Android abstract namespace local socket address
  - fixed GH #2038: Poco::Net::MultipartWriter::createBoundary() always returns the same string.
  - fixed GH #2020: SQLite not handling parameter count mismatch correctly.
  - fixed GH #2012: Data/SQLite: Exception messages contain duplicate text
  - fixed GH #2005: Upgraded bundled PCRE to 8.41
  - fixed GH #2000: Fix building XMLStreamParser with unbundled expat
  - fixed GH #1603: fix MinGW 4.8.2 Compilation
  - fixed GH #1991: Support building poco 1.8.0 as cmake sub-project
  - fixed GH #2080: Bugs in Poco::Net::Socket::select when POCO_HAVE_FD_POLL is defined


!!!Release *******

!!Summary of Changes

  - Reverted change for GH #1828; DeflatingStreamBuf::sync() no longer flushes
    underlying stream as this causes corruption for some Zip files.
  - PocoDoc: fix for handling compiler configuration for Gradle builds.


!!!Release 1.8.0

!!Summary of Changes

  - Poco::Base64Encoder: add support for base64url encoding (GH #1967)
  - Add Poco::Net::PollSet class to Net library (GH #1763)
  - The Net library now supports Unix Domain Sockets, where available.
  - Added stream parser (Poco::XML::XMLStreamParser) to XML library (GH #1697)
  - Added Poco::Net::TCPServerConnectionFilter and Poco::Net::TCPServer::setConnectionFilter()
    to support connection filtering and IP blacklisting (GH #1485)
  - Added Redis library (GH #1383)
  - Added Zip64 support to Zip library (GH #1356)
  - Upgraded bundled SQLite to 3.21.0
  - Removed OpenVMS support (GH #1988)
  - fixed GH #271: NamedMutex_UNIX.cpp must remove semid
  - fixed GH #739: Add Poco::Net::WebSocket::receiveFrame() that appends to a Poco::Buffer<char>
  - fixed GH #749: NTP Packet impl not according to RFC958
  - fixed GH #896: Sample "TwitterClient" of NetSSL_OpenSSL can't be build
  - fixed GH #1172: Poco::Data default storage should be std::vector
  - fixed GH #1337: Poco::HTMLForm throws exception HTMLFormException("Form must be prepared")
    even after form is prepared.
  - fixed GH #1373: SessionImpl::close() does not check return code of close handle specific function
  - fixed GH #1425: Workaround bug in SolarisStudio 12.4 on RVO-ed objects.
  - fixed GH #1614: Problematic license for JSON component: the previously used JSON.org parser
    has been replaced with pdjson
  - fixed GH #1659: wrong field size calculation in ODBC code
  - fixed GH #1683: Poco::Data ODBC impl doesn't bind to unsigned numeric types properly
  - fixed GH #1705: MongoDB: support URI in Connection
  - fixed GH #1708: "SocketReactor::addEventHandler" and "SocketReactor::removeEventHandler"
    must protect the access to "NotifierPtr pNotifier"
  - fixed GH #1729: getConnectionTimeout of SQLite DB wrapper returns wrong value
    (in milliseconds, should be in seconds)
  - fixed GH #1739: OpenSSLInitializer isn't threadsafe
  - fixed GH #1750: double_conversion in NumericString is in conflict with Qt5 Core
  - fixed GH #1804 and GH #1805: Integer Overflow or Wraparound
  - fixed GH #1828: DeflatingStreamBuf::sync() should also flush underlying stream.
  - fixed GH #1880: FTPClientSession::close() error
  - fixed GH #1897: DateTime wrong binding/extraction for MySQL database
  - fixed GH #1905: Compiling Foundation library with POCO_NO_FPENVIRONMENT in Config.h fails
  - fixed GH #1906: Race condition in ThreadPool
  - fixed GH #1913: Message Doesn't Support 64-bit Thread IDs
  - fixed GH #1921: ICMPSocket does not check reply address
  - fixed GH #1926: Exception when using SortedDirectoryIterator
  - fixed GH #1934: Poco::File::setExecutable() on POSIX should set executable bit for group and
    other if corresponding readable bit is set
  - fixed GH #1950: Net Exception: Address family not supported with clang
  - fixed GH #1964: Buffer<> swap miss ownMem

!!Incompatible Changes and Possible Transition Issues

  - Crypto and NetSSL on Windows: The included Visual Studio project files now expect the
    OpenSSL headers and libraries to be in the "openssl" directory in the POCO root
    directory, alongside the Foundation, XML, Net directories. The GitHub repository
    contains the <*openssl*> submodule, which has the required files. The release
    source packages do not contain these files. They are available from the
    [[https://github.com/pocoproject/openssl pocoproject/openssl]] repository
    ([[https://github.com/pocoproject/openssl/archive/develop.zip Zip archive]]).
    You can also provide your own build of OpenSSL, by specifying the appropriate
    header and library search paths in the Visual Studio project files. Through the
    <*Poco/Crypto/Crypto.h*> and <*Poco/Net/NetSSL.h*> headers, the <*libcrypto.lib*>
    and <*libssl.lib*> libraries will be automatically linked. If you don't want this,
    build Crypto and NetSSL_OpenSSL with the <[POCO_EXTERNAL_OPENSSL]> macro defined.


!!!Release 1.7.9p2

!!Summary of Changes

  - fixed GH #1628: Export Poco::Zip::ZipUtil class


!!!Release 1.7.9p1

!!Summary of Changes

  - fixed GH #1968: Zip Decompress Parent Path Injection


!!!Release 1.7.9

  - fixed GH #1813: xmlparse.cpp doesn't compile in WinCE (poco 1.7.8p3)
  - fixed GH #1826: XPath query error
  - fixed GH #1834: Visual Studio 2008 cannot find stdint.h
  - fixed GH #1842: Upgrade bundled expat to 2.2.3
  - fixed GH #1843: Use random salt for Poco::XML::NamePool
  - fixed GH #1865: AbstractEvent::hasDelegates() is not thread-safe
  - improved/fixed QNX support
  - Poco::Util::LayeredConfiguration: added support for labelling configurations and
    finding them by their label
  - upgraded bundled SQLite to 3.20.1
  - PageCompiler: support <%@ include file="<path>" %> syntax for includes, in addition
    to <%@ include page="<path>" %>
  - PageCompiler: optimize generated request handler code by removing useless
    statements, e.g. writing empty strings.
  - added POCO_DEPRECATED macro which will be used in the future to deprecate
    classes and methods.
  - Poco::NamedMutex and Poco::NamedEvent (System V Semaphores implementation): files are
    now opened with O_RDONLY | O_CREAT instead of O_WRONLY | O_CREAT, allowing sharing
    between different users. Furthermore, ftok() is called with 'p' as project ID
    argument.


!!!Release 1.7.8p3

  - fixed GH #1760: Upgrade bundled expat to 2.2.1 which fixes some vulnerabilities:
    http://seclists.org/oss-sec/2017/q2/499


!!!Release 1.7.8p2

!!Summary of Changes

  - fixed GH #1655: CipherImpl memory leak with OpenSSL 1.1


!!!Release 1.7.8

!!Summary of Changes

  - fixed GH #1212: Lost WebSocket Frames after Client Websocket Handshake is complete
  - fixed GH #1260: URI encoding
  - fixed GH #1501: Alpine 3.4 trouble with Foundation/src/Error.cpp
  - fixed GH #1523: Long path names under Windows
  - fixed GH #1536: Building with OS X 10.12 SDK and 10.7 deployment target without libc++ fails
  - fixed GH #1537: Need to add multiple cflags parameters to configure
  - fixed GH #1539: Allow overriding POCO_TARGET_OSARCH for iPhoneSimulator
  - fixed GH #1546: Enable bitcode for iPhone build config
  - fixed GH #1549: Latin2Encoding and 0xFF
  - fixed GH #1551: Unable to use Poco on macOS 10.12
  - fixed GH #1552: IPv6 & operator throws an exception when scope = 0
  - fixed GH #1566: Poco/Zip issue with some CM_DEFLATE archives
  - fixed GH #1567: Poco/ZIP issue with uncompressed archives
  - fixed GH #1570: IPv6AddressImpl::toString() returns wrong output for IPv6 address "::"
  - fixed GH #1571: ODBC Preparator memory leak
  - fixed GH #1573: Poco::File::createDirectories() should not throw Poco::FileExistsException
  - fixed GH #1580: Unable to unzip zip file created using non-seeking stream
  - fixed GH #1581: Cannot find 'pcre.h' when using POCO_UNBUNDLED, a non-system PCRE, and CMake
  - fixed GH #1588: Poco::Net::HTTPChunkedStreamBuf::readFromDevice(): restrict maximum
    size of chunk length
  - fixed GH #1589: Poco::Net::HTMLForm: restrict maximum field and value length
  - fixed GH #1590: Poco::Net::DialogSocket: restrict maximum line length
  - fixed GH #1591: Poco::Net::MultipartReader: restrict maximum boundary string length
  - fixed GH #1597: adding empty file to zip leads to archive that can't be unzipped by windows
  - fixed GH #1599: readFromDevice() in AutoDetectStream.cpp in Poco Zip cannot detect signature
  - fixed GH #1534: Upgraded bundled zlib to 1.2.11
  - fixed GH #1558: Upgraded bundled SQLite to 3.16.2
  - fixed GH #1586: Upgraded bundled PCRE to 8.40
  - fixed GH #1538: Upgraded bundled double-conversion to 1.1.5
  - MongoDB: added support for authentication using "MONGODB-CR" and "SCRAM-SHA-1"
    authentication schemes.

!!Incompatible Changes and Possible Transition Issues

  - MongoDB: additional documentation and fixes for style and consistency and minor
    API improvements (e.g., Poco::MongoDB::Binary)
    Note: some flag enumeration values have been renamed for better consistency
    and readability; existing code using these will have to be updated.


!!!Release 1.7.7

!!Summary of Changes

  - fixed GH #865: FileChannel compress fails leaving empty .gz files
  - fixed GH #990: Potential race condition in Poco::File on Windows
  - fixed GH #1157: Fixing a bug in the NetSSL_Win module (Host name verification failed error)
  - fixed GH #1351: Fix for android include pthread.h from /usr/include
  - fixed GH #1436: ODBC Bug: Unicode text(NVARCHAT) read from DB is truncated to half
  - fixed GH #1453: _clock_gettime Symbol not found on Mac 10.11
  - fixed GH #1460: POCO does not build with OpenSSL 1.1
  - fixed GH #1461: Poco::Data::SQLite::SQLiteStatementImpl::next() error
  - fixed GH #1462: AbstractConfiguration::getUInt does not parse hex numbers
  - fixed GH #1464: ODBCMetaColumn::init() always maps integer NUMERIC/DECIMAL to Int32
  - fixed GH #1465: Assertion violation in DateTime.cpp using ZipArchive
  - fixed GH #1472: HTTP(S)StreamFactory should send a User-Agent header.
  - fixed GH #1476: Fixed error with Poco::UTF8Encoding::isLegal()
  - fixed GH #1484: ODBC: fix uninitialized variable
  - fixed GH #1486: Support ODBC GUID data type as string
  - fixed GH #1488: Poco::ObjectPool shrinks if returned object is not valid
  - fixed GH #1515: Detection of closed websocket connection
  - fixed GH #1521: bug in JSON ParseHandler.cpp (empty keys should be valid)
  - fixed GH #1526: iOS app rejected, IPv6 not working
  - fixed GH #1532: RecordSet and RowFilter: bad use of reference counter


!!!Release 1.7.6

!!Summary of Changes

  - fixed GH #1298: ZipFileInfo: Assertion violation when reading ods files
  - fixed GH #1315: Redefine Poco assertions for static analysis
  - fixed GH #1397: Fix issues reported by static source code analysis
  - fixed GH #1403: Android compile with poco-1.7.5 no 'pthread_condattr_setclock' error
  - fixed GH #1416: Assertion violation when unzipping
  - fixed GH #1418: Poco::Delegate assignment operator fails to compile for some specializations
  - fixed GH #1422: Can't build poco 1.7.4 or 1.7.5 on centos5 32 bit
  - fixed GH #1429: exception thrown in MongoDB when using replicaset
  - fixed GH #1431: Poco/FIFOBuffer.h copy issue
  - fixed GH #1445: Use stable_sort to preserve order of IP addresses from DNS
  - fixed GH #1456: better handle leap seconds in Poco::DateTime and Poco::LocalDateTime
  - fixed GH #1458: Probably invalid epoll_create() usage inside Poco/Socket.cpp
  - Poco::XML::NamePool: increased default size from 251 to 509. Default size can now
    be changed by defining the POCO_XML_NAMEPOOL_DEFAULT_SIZE macro accordingly.
  - Enchancements: Poco::XML::Document and Poco::XML::DOMParser have new constructors
    taking a NamePool size. Poco::Util::XMLConfiguration::load() also has a new overload
    for that purpose.
  - Improved error handling in the Zip library (getting rid of some poco_assert macros
    and did proper error handling instead).
  - Added Poco::URISyntaxException (subclass of Poco::SyntaxException), which is now
    thrown by Poco::URI.
  - Improved error handling in Poco::URIStreamOpener::open().
  - Poco::Data::MySQL: Handle connection lost/server gone error when starting a transaction
    and retry.
  - XMLConfiguration default (and single-argument delimiter) constructor now loads an empty
    XML document with "config" root element to make the configuration usable without an
    additional call to load() or loadEmpty().


!!!Release 1.7.5

!!Summary of Changes

  - fixed GH #1252: Unable to compile Poco::Data for Windows Compact Embedded 2013
  - fixed GH #1344: Poco::Event::wait(timeout) should use CLOCK_MONOTONIC on Linux
  - fixed GH #1355: [JSON::Object] After copy-ctor, JSON::Object::_keys still points to
    keys in map of copied object
  - GH #1361: Shell expansion rules say that tilde must be replaced with $HOME before
    calling getpwuid
  - Poco::SingletonHolder: added reset() method
  - prefer clock_getttime() over gettimeofday() if available
  - Upgraded bundled SQLite to 3.14.1


!!!Release 1.7.4

!!Summary of Changes

  - fixed GH #1300: Session constructor hangs
  - fixed GH #1303: HTTPSClientSession::sendRequest() fails if server has wildcard cert
  - fixed GH #1304: URI doesn't know "ws:/" or "wss://" schemes
  - fixed GH #1307: Upgrade bundled expat to 2.2.0
  - fixed GH #1316: Empty SocketReactor never sleeps
  - fixed GH #1313: XML library compilation error
  - Upgraded bundled SQLite to 3.13.0


!!!Release 1.7.3

!!Summary of Changes

  - fixed GH #993: Invalid zip format when opening a docx in word
  - fixed GH #1235: Poco::Net::HTTPClientSession::sendRequest() should also handle HTTP_PATCH
  - fixed GH #1236: Remove Poco::Data::Row::checkEmpty() as it prevents Row from being used
    with all NULL rows
  - fixed GH #1239: Poco::Zip::Compress with non-seekable stream fails for CM_STORE
  - fixed GH #1242: Poco::Data::RowFormatter generate exception if the first column of first
    row is null
  - fixed GH #1253: ListMap does not maintain insertion order if key already exists
  - Upgraded bundled SQLite to 3.12.2


!!!Release 1.7.2

!!Summary of Changes

  - fixed GH #1197: Upgrade bundled expat to 2.1.1
    Expat 2.1.1 fixes a CVE: https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2015-1283
  - fixed GH #1204: getdtablesize has been removed on Android 21
  - fixed GH #1203: Poco::Data::RecordSet should be reusable
  - fixed GH #1198: Upgrade bundled SQLite to 3.12.1


!!!Release 1.7.1

!!Summary of Changes

  - fixed GH #1187: Data/MySQL: Seeing frequent "MySQL server has gone away" errors
  - fixed GH #1184: Attempting to connect via a proxy throws a DNS error "Host not found"
  - fixed GH #1180: Possible deadlock when TaskManager::count() is called in onFinished
  - NetSSL_OpenSSL: use TLS_*_method() instead of deprecated SSLv23_*_method()
    if OpenSSL version is >= 1.1; initialize default/fallback client context to support
    all TLS protocols, not just TLSv1


!!!Release 1.7.0

!!Summary of Changes

  - POSSIBLE BREAKING CHANGE: removed automatic registration of Data connectors due to
    issues with static initialization order.
  - NetSSL_OpenSSL: added support for ECDH and DH ciphers; added support to disable
    specific protocols (Poco::Net::Context::disableProtocols());
    new Poco::Net::Context constructor taking a Poco::Net::Context::Params structure that
    allows specifying ECDH and DH parameters.
  - Poco::Net::TCPServer: add additional try ... catch block around poll() to
    gracefully deal with errors due to high system load (e.g., out of file descriptors).
  - fixed GH #1171: Poco::Data::RecordSet: rowCount not reset after execute
  - fixed GH #1167: CMake & POCO_UNBUNDLED: expat sources are compiled in libPocoXML
  - fixed GH #1160: Poco::Net::NetException
    "SSL Exception: error:1409F07F:SSL routines:ssl3_write_pending:bad write retry"
  - fixed GH #1152: Wrong TaskProgressNotification description
  - fixed GH #1141: Poco::StringTokenizer::TOK_TRIM changes behavior between 1.4 and 1.6
  - fixed GH #1137: Missing 'longint' type in SQLite
  - fixed GH #1135: Different package on github and official web site
  - fixed GH #1030: tvOS / WatchOS bitcode enabled for simulators
  - fixed GH #1114: World-write permissions on files created by daemon
  - fixed GH #1087: prevent line breaks in base64-encoded creds
  - fixed GH #1026: Fixes for producing the poco-1.6.2 release on a Cygwin x86 platform
  - fixed GH #1022: Abbreviation in setThreadName can happen even if thread name is not too long
  - fixed GH #1002: ActiveDispatcher saves reference to event context after event was
    performed until it gets new event
  - fixed GH #973: overwrite existing files on windows when moving files
  - fixed GH #969: Poco::File::renameTo() behaviour differs on windows and linux
  - fixed GH #967: Missing data types in SQLite
  - fixed GH #966: Possible crash when processing a corrupted Zip file
  - fixed GH #958: Bug while reading X509Certificate subjectName
  - fixed GH #937: Missing build_vs140.cmd
  - fixed GH #933: Change in JSON::Object::set(key,value) behavior in 1.6.1
  - fixed GH #931: make strToInt() more strict in what it accepts
  - fixed GH #921: `BasicUnbufferedStreamBuf` needs to be marked for import/export
  - fixed GH #848: MailMessage::_encoding is not set when retrieving plain/text message
  - fixed GH #767: Inconsistency in getPath & getPathAndQuery returns
  - fixed GH #724: Poco 1.6.0 is not compiled with openssl 1.0.0
  - fixed GH #713: Improved support for producing Canonical XML in XMLWriter
  - fixed GH #696: bug in parsing name of attachment poco c++ 1.6.0
  - fixed GH #335: Compress with nonseekable
  - upgraded bundled SQLite to 3.11.0
  - added Poco::Crypto::X509Certificate::equals() to compare two certificates
  - support for detecting Win8/Win10 in Poco::Environment
  - Poco::Net::HTTPServerRequestImpl: fixed an issue with DELETE in persistent connections
  - NetSSL: added Context::preferServerCiphers()
  - NetSSL: added support for ECDH, new Context constructor
  - NetSSL: add support for disabling certain protocols
  - SMTPClientSession: added support for XOAUTH2 authentication
  - Poco::Data::SessionPool: re-added customizeSession() method from 1.4.x releases
  - improved SSLManager to automatically set-up a reasonable client Context if
    none is configured
  - add brew OpenSSL search paths to Darwin configs
  - add HTTP/1.1 version to HTTPRequest for client WebSocket, as this is required for
    most servers
  - remove GCC_DIAG_OFF as this caused more issues than it solved
  - respect POCO_NO_FORK_EXEC in ServerApplication (tvOS)
  - tvOS and WatchOS support
  - fix: need an implementation of available() for WebSocketImpl
  - HTTPSessionInstantiator: respect global proxy config
  - added constant for HTTP PATCH method to Poco::Net::HTTPRequest
  - NumberParser::parseHex[64](): allow 0x/0X prefix

!!Incompatible Changes and Possible Transition Issues

  - Removed automatic registration of Data connectors due to issues with static
    initialization order. Data connectors used in an application must be explicitly
    registered with a call to registerConnector() before it can be used, e.g.:
    Poco::Data::SQLite::Connector::registerConnector()


!!!Release 1.6.1

!!Summary of Changes

  - added project and solution files for Visual Studio 2015
  - upgraded bundled SQLite to ********
  - fixed GH #782: Poco::JSON::PrintHandler not working for nested arrays
  - fixed GH #819: JSON Stringifier fails with preserve insert order
  - fixed GH #878: UUID tryParse
  - fixed GH #869: FIFOBuffer::read(T*, std::size_t) documentation inaccurate
  - fixed GH #861: Var BadCastException
  - fixed GH #779: BUG in 1.6.0 Zip code
  - fixed GH #769: Poco::Var operator== throws exception
  - fixed GH #766: Poco::JSON::PrintHandler not working for objects in array
  - fixed GH #763: Unable to build static with NetSSL_OpenSSL for OS X
  - fixed GH #750: BsonWriter::write<Binary::Ptr> missing size ?
  - fixed GH #741: Timestamp anomaly in Poco::Logger on WindowsCE
  - fixed GH #735: WEC2013 build fails due to missing Poco::Path methods.
  - fixed GH #722: poco-1.6.0: Unicode Converter Test confuses string and char types
  - fixed GH #719: StreamSocket::receiveBytes and FIFOBuffer issue in 1.6
  - fixed GH #706: POCO1.6 Sample EchoServer BUG
  - fixed GH #646: Prevent possible data race in access to Timer::_periodicInerval
  - DeflatingStream: do not flush underlying stream on sync() as these can cause
    corrupted files in Zip archives


!!!Release 1.6.0

!!Summary of Changes

  - fixed GH #625: MongoDB ensureIndex double insert?
  - fixed GH #622: Crypto: RSATest::testSign() should verify with public key only
  - fixed GH #620: Data documentation sample code outdated
  - fixed GH #618: OS X 10.10 defines PAGE_SIZE macro, conflicts with PAGE_SIZE in Thread_POSIX.cpp
  - fixed GH #616: Visual Studio warning C4244
  - fixed GH #612: OpenSSLInitializer calls OPENSSL_config but not CONF_modules_free
  - fixed GH #608: (Parallel)SocketAcceptor ctor/dtor call virtual functions
  - fixed GH #607: Idle Reactor high CPU usage
  - fixed GH #606: HTMLForm constructor read application/x-www-form-urlencoded UTF-8 request
    body first parameter with BOM in name
  - fixed GH #596: For OpenSSL 1.0.1, include openssl/crypto.h not openssl/fips.h
  - fixed GH #592: Incorrect format string in Poco::Dynamic::Struct
  - fixed GH #590: Poco::Data::SQlite doesn't support URI filenames
  - fixed GH #564: URI::encode
  - fixed GH #560: DateTime class calculates a wrong day
  - fixed GH #549: Memory allocation is not safe between fork() and execve()
  - fixed GH #500: SSLManager causes a crash
  - fixed GH #490: 2 byte frame with payload length of 0 throws "Incomplete Frame Received" exception
  - fixed GH #483: multiple cases for sqlite_busy
  - fixed GH #482: Poco::JSON::Stringifier::stringify bad behaviour
  - fixed GH #478: HTTPCredentials not according to HTTP spec
  - fixed GH #471: vs2010 release builds have optimization disabled ?
  - fixed GH #468: HTTPClientSession/HTTPResponse not forwarding exceptions
  - fixed GH #438: Poco::File::setLastModified() doesn't work
  - fixed GH #402: StreamSocket::receiveBytes(FIFOBuffer&) and sendBytes(FIFOBuffer&) are
    not thread safe
  - fixed GH #345: Linker warning LNK4221 in Foundation for SignalHandler.obj, String.obj
    and ByteOrder.obj
  - fixed GH #331: Poco::Zip does not support files with ".." in the name.
  - fixed GH #318: Logger local time doesn't automatically account for DST
  - fixed GH #294: Poco::Net::TCPServerParams::setMaxThreads(int count) will not accept count == 0.
  - fixed GH #215: develop WinCE build broken
  - fixed GH #63: Net::NameValueCollection::size() returns int
  - Poco::Logger: formatting methods now support up to 10 arguments.
  - added Poco::Timestamp::raw()
  - Poco::DeflatingOutputStream and Poco::InflatingOutputStreams also flush underlying stream
    on flush()/sync().
  - Poco::Util::Timer: prevent re-schedule of cancelled TimerTask
  - enabled WinRegistryKey and WinRegistryConfiguration for WinCE
  - Poco::BasicEvent improvements and preparations for future support of lambdas/std::function
  - upgraded bundled sqlite to *******
  - Poco::Thread: added support for starting functors/lambdas
  - Poco::Net::HTTPClientSession: added support for global proxy configuration
  - added support for OAuth 1.0/2.0 via Poco::Net::OAuth10Credentials and
    Poco::Net::OAuth20Credentials classes.
  - Poco::Net::IPAddress: fixed IPv6 prefix handling issue on Windows
  - added Poco::Timestamp::TIMEVAL_MIN and Poco::Timestamp::TIMEVAL_MAX
  - added Poco::Clock::CLOCKVAL_MIN and Poco::Clock::CLOCKVAL_MAX
  - added poco_assert_msg() and poco_assert_msg_dbg() macros
  - Poco::Net::Context: fixed a memory leak if the CA file was not found while creating the
    Context object (the underlying OpenSSL context would leak)
  - Poco::URI: added new constructor to create URI from Path
  - Various documentation and style fixes
  - Removed support (project/solution files) for Visual Studio.NET 2003 and Visual Studio 2005.
  - Improved CMake support


!!Incompatible Changes and Possible Transition Issues

  - Compiling POCO on Windows without #define POCO_WIN32_UTF8 is deprecated and will
    lead to diagnostic messages while compiling.
  - Support (project and solution files) for MS Visual Studio 2003 and 2005 has been
    removed; the oldest officially supported VS version is 2008 (MSVC version 9.0).
  - MongoDB: The ObjectId class has a new constructor taking a std::string containing a
    hexadecimal representation of the object ID.


!!!Release 1.5.4

!!Summary of Changes

  - fixed GH #326: compile Net lib 1.5.2 without UTF8 support enabled
  - fixed GH #518: NetworkInterface.cpp compile error w/ POCO_NO_WSTRING (1.5.3)
  - Fixed MSVC 2010 warnings on large alignment
  - make HTTPAuthenticationParams::parse() add value on end of string
  - fixed GH #482: Poco::JSON::Stringifier::stringify bad behaviour
  - fixed GH #508: Can't compile for arm64 architecture
  - fixed GH #510: Incorrect RSAKey construction from istream
  - fix SharedMemory for WinCE/WEC2013
  - Add NIOS2 double conversion detection, fixes compile errors
  - added VS2013 project/solution files for Windows Embedded Compact 2013
  - added Process::isRunning()
  - NetSSL: Fix typo in documentation
  - NetSSL_OpenSSL: support for TLS 1.1 and 1.2
  - Zip: Added CM_AUTO, which automatically selects CM_STORE or CM_DEFLATE based
    on file extension. Used to avoid double-compression of already compressed file
    formats such as images.
  - added %L modifier to PatternFormatter to switch to local time
  - removed unnecessary explicit in some multi-arg constructors
  - Allow SecureStreamSocket::attach() to be used in server connections
  - added Var::isBoolean() and fixed JSON stringifier
  - added poco_unexpected() macro invoking Bugcheck::unexpected() to deal
    with unexpected exceptions in destructors
  - fixed GH #538 prevent destructors from throwing exceptions
  - improved HTTP server handling of errors while reading header
  - fixed GH #545: use short for sign
  - upgraded SQLite to 3.8.6
  - fixed GH #550 WebSocket fragmented message problem
  - improved HTTPClientSession handling of network errors while sending the request
  - updated bundled PCRE to 8.35.0
  - fixed GH #552: FIFOBuffer drain() problem
  - fixed GH #402: StreamSocket::receiveBytes(FIFOBuffer&) and sendBytes(FIFOBuffer&) are
    not thread safe
  - HTTPCookie: fix documentation for max age
  - added Timestamp::raw() and Clock::raw()
  - Poco::Buffer properly handles zero-sized buffers
  - GH #512: Poco:Data:ODBC:Binder.h causes a crash
  - Added Crypto_Win and NetSSL_Win libraries which are re-implementations of existing
    Crypto and NetSSL_OpenSSL libraries based on WinCrypt/Schannel. The new libraries
    can be used as an almost drop-in replacement for the OpenSSL based libraries on
    Windows and Windows Embedded Compact platforms. Only available from GitHub for now.


!!!Release 1.5.3

!!Summary of Changes

  - fixed GH #316: Poco::DateTimeFormatter::append() gives wrong result for
    Poco::LocalDateTime
  - Poco::Data::MySQL: added SQLite thread cleanup handler
  - Poco::Net::X509Certificate: improved and fixed domain name verification for
    wildcard domains
  - added Poco::Clock class, which uses a system-provided monotonic clock
    (if available) and is thus not affected by system realtime clock changes.
    Monotonic Clock is available on Windows, Linux, OS X and on POSIX platforms
    supporting clock_gettime() and CLOCK_MONOTONIC.
  - Poco::Timer, Poco::Stopwatch, Poco::TimedNotificationQueue and Poco::Util::Timer
    have been changed to use Poco::Clock instead of Poco::Timestamp and are now
    unaffected by system realtime clock changes.
  - fixed GH #350: Memory leak in Data/ODBC with BLOB
  - Correctly set MySQL time_type for Poco::Data::Date.
  - fixed GH #352: Removed redundant #includes and fixed spelling mistakes.
  - fixed setting of MYSQL_BIND is_unsigned value.
  - fixed GH #360: CMakeLists foundation: add Clock.cpp in the list of source files
  - Add extern "C" around <net/if.h> on HPUX platform.
  - added runtests.sh
  - fixed CPPUNIT_IGNORE parsing
  - fixed Glob from start path, for platforms not alowing transverse from root (Android)
  - added NTPClient (Rangel Reale)
  - added PowerShell build script
  - added SmartOS build support
  - fix warnings in headers
  - XMLWriter: removed unnecessary apostrophe escaping (&apos)
  - MongoDB: use Int32 for messageLength
  - fixed GH #380: SecureSocket+DialogSocket crashes with SIGSEGV when timeout occours
  - Improve RSADigestEngine, using Poco::Crypto::DigestEngine to calculate hash before signing
  - added Poco::PBKDF2Engine
  - Fixed GH #380: SecureSocket+DialogSocket crashes with SIGSEGV when timeout occours
  - added support for a 'Priority' attribute on cookies.
  - GH #386: fixed bug in MailMessage without content-transfer-encoding header
  - GH #384: ew hash algorithms support for RSADigestEngine
  - fixed Clock overflow bug on Windows
  - Poco::ByteOrder now uses intrinsics, if available
  - CMake: added /bigobj option for msvc
  - Fix typo to restore Net/TestSuite_x64_vs120 build
  - correct path for CONFIGURE_FILE in CMakeLists.txt
  - Building Poco 1.5.2 for Synology RS812+ (Intel Atom) (honor POCO_NO_INOTIFY)
  - added WEC2013 support to buildwin.cmd and buildwin.ps1
  - HTMLForm: in URL encoding, percent-encode more characters
  - Fixed #include <linux/if.h> conflict with other libraries
  - Poco::Net::X509Certificate::verify() no longer uses DNS reverse lookups to validate host names
  - cert hostname validation is case insensitive and stricter for wildcard certificates
  - TCPServer: do not reduce the capacity of the default ThreadPool
  - added POCO_LOG_DEBUG flag
  - Zip: fixed a crash caused by an I/O error
  - added runtest script for windows
  - added SQlite Full Text Search support
  - added Thread::trySleep() and Thread::wakeUp()
  - fixed GH #410: Bug in JSON::Object.stringify() in 1.5.2
  - fixed GH #362: Defect in Var::parseString when there is no space between value and newline
  - fixed GH #314: JSON parsing bug
  - added GH #313: MetaColumn additions for Data::ODBC and Data::SQLite
  - fixed GH #346: Make Poco::Data::Date and Poco::Data::Time compare functions const.
  - fixed GH #341: Compiling poco-1.5.2 for Cygwin
  - fixed GH #305: There are bugs in Buffer.h
  - fixed GH #321: trivial build fixes (BB QNX build)
  - fixed GH #440: MongoDB ObjectId string formatting
  - added SevenZip library (Guenter Obiltschnig)
  - fixed GH #442: Use correct prefix length field of Windows IP_ADAPTER_PREFIX structure
  - improved GH #328: NetworkInterface on Windows XP
  - fixed GH #154 Add support for MYSQL_TYPE_NEWDECIMAL to Poco::Data::MySQL
  - fixed GH #290: Unicode support
  - fixed GH #318: Logger local time doesn't automatically account for DST
  - fixed GH #363: DateTimeParser tryParse/parse
  - added HTMLForm Content-Length calculation (Rangel Reale)
  - Make TemporaryFile append a slash to tempDir
  - Make TemporaryFile append a slash to tempDir
  - fixed GH #319 android build with cmake
  - added hasDelegates() method to AbstractEvent
  - fixed GH #230: Poco::Timer problem
  - fixed GH #317: Poco::Zip does not support newer Zip file versions.
  - fixed GH #176: Poco::JSON::Stringifier UTF encoding
  - fixed GH #458: Broadcast address and subnet mask for IEEE802.11 network interface
  - fixed GH #456: poco: library install dirs per RUNTIME/LIBRARY/ARCHIVE

!!Incompatible Changes and Possible Transition Issues

  - Data::ODBC: UTF-16 Unicode is now directly mapped and recognized as type by ODBC.
    This may cause behavior different from previosu versions, especially with Any and
    Dynamic::Var bindings.
    In this release, UTF-16 binding is only available for ODBC back end; although other
    back ends will compile with UTF-16 strings bound, such binding attempt will throw
    NotImplementedException at runtime.
  - Please note that 1.5.x releases are development releases and not considered stable. Interfaces may
    change, and backwards compatibility with the stable 1.4 release series
    is not guaranteed. There may also be some rough edges.
    The next stable release incorporating 1.5 features will be 1.6.


!!!Release 1.5.2

!!Summary of Changes

  - added MongoDB library
  - fixed GH #57: poco-1.5.1: Doesn't compile for Android
  - added VoidEvent (Arturo Castro)
  - fixed GH #80: NumberFormatter::append broken
  - fixed GH #93: ParallelSocketAcceptor virtual functions
  - optional small object optimization for IPAddress, SocketAddress, Any and Dynamic::Var
  - SQLite events (insert, update, delete, commit, rollback) handlers
  - merged GH #91: Improve SQLite multi-threaded use (Rangel Reale)
  - merged GH #86: Invalid pointers to vector internals (Adrian Imboden)
  - automatic library initialization macros
  - fixed GH #110: WebSocket accept() fails when Connection header contains multiple tokens
  - fixed GH #71: WebSocket and broken Timeouts (POCO_BROKEN_TIMEOUTS)
  - fixed a warning in Poco/Crypto/OpenSSLInitializer.h
  - fixed GH #109: Bug in Poco::Net::SMTPClientSession::loginUsingPlain
  - added clang libc++ build configurations for Darwin and iPhone (Andrea Bigagli)
  - fixed GH #116: Wrong timezone parsing in DateTimeParse (Matej Knopp)
  - fixed GH #118: JSON::Object::stringify endless loop
  - added Recursive and SortedDirectoryIterator (Marian Krivos)
  - added ListMap (map-like container with preserving insertion order)
  - MailMessage: attachments saving support and consistent read/write
  - fixed GH #124: Possible buffer overrun in Foundation/EventLogChannel
  - fixed GH #119: JSON::Object holds values in ordered map
  - added JSON::PrintHandler
  - renamed JSON::DefaultHandler to ParseHandler (breaking change!)
  - fixed GH #127: Eliminate -Wshadow warnings
  - fixed GH #79: Poco::Thread leak on Linux
  - fixed GH #61: static_md build configs for Crypto and NetSSL
  - fixed GH #130: prefer sysconf over sysctlbyname
  - fixed GH #131: no timezone global var on OpenBSD
  - fixed GH #102: Some subprojects don't have x64 solutions for VS 2010
  - added GH #75: Poco::Uri addQueryParameter method
  - Poco::Environment::osDisplayName() now recognizes Windows 8/Server 2012
  - fixed GH #140: Poco::Runnable threading cleanup issue
  - simplified default TCP/HTTPServer construction
  - fixed GH #141: Application::run() documentation/implementation discrepancy
  - changed RowFormatter to SharedPtr<RowFormatter> in Data::RecordSet interface (breaking change!)
  - fixed GH #144: Poco::Dynamic emits invalid JSON
  - removed naked pointers from Data interfaces
  - fixed GH #82: name conflict in Data::Keywords::bind
  - fixed GH #157: MySQL: cannot bind to 'long' data type on Windows/Visual C++
  - fixed GH #158: MySQL: MYSQL_BIND 'is_unsigned' member is not set
  - fixed GH #160: MultipartReader ignores first part, if preamble is missing
  - fixed GH #156: Possible buffer overrun in Foundation/EventLogChannel
  - XML: fixed an issue with parsing a memory buffer > 2 GB
  - upgraded to expat 2.1.0
  - Data/ODBC: added support for setting query timeout (via setProperty
    of "queryTimeout"). Timeout is int, given in seconds.
  - fixed a potential endless loop in SecureStreamSocketImpl::sendBytes()
    and also removed unnecessary code.
  - fixed GH #159: Crash in openssl CRYPTO_thread_id() after library libPocoCrypto.so
    has been unloaded.
  - fixed GH #155: MailOutputStream mangles consecutive newline sequences
  - fixed GH #139: FileChannel::PROP_FLUSH is invalid (contains a tab character)
  - fixed GH #173: HTTPClientSession::proxyConnect forces DNS lookup of host names
  - fixed GH #194: MessageNotification constructor is inefficient.
  - fixed GH #189: Poco::NumberParser::tryParse() documentation bug
  - fixed GH #172: IPv6 Host field is stripped of Brackets in HTTPClientSession
  - fixed GH #188: Net: SocketAddress operator < unusable for std::map key
  - fixed GH #128: DOMWriter incorrectly adds SYSTEM keyword to DTD if PUBLIC is
    already specified
  - fixed GH #65: Poco::format() misorders sign and padding specifiers
  - upgraded bundled SQLite to 3.7.17
  - replaced JSON parser with Poco::Web::JSON parser (from sandbox)
  - added JSON conversion to Dynamic Struct and Array
  - added VarIterator
  - modified behavior of empty Var (empty == empty)
  - added Alignment.h header for C++03 alignment needs
  - added Data/WebNotifier (DB, WebSocket) example
  - fixed GH #209: Poco::NumberFormatter double length
  - fixed GH #204: Upgrade zlib to 1.2.8
  - fixed GH #198: The "application.configDir" property is not always created.
  - fixed GH #185: Poco::NumberFormatter::format(double value, int precision)
    ignore precision == 0
  - fixed GH #138: FreeBSD JSON tests fail
  - fixed GH #99: JSON::Query an JSON::Object
  - limited allowed types for JSON::Query to Object, Array, Object::Ptr,
    Array::Ptr and empty
  - fixed GH #175: HTMLForm does not read URL parameters on POST or PUT
  - added GH #187: MySQL: allow access to the underlying connection handle
  - added GH #186: MySQL: support for MYSQL_SECURE_AUTH
  - fixed GH #174: MySQL: 4GB allocated when reading any largetext or largeblob field
  - fixed a potential memory leak in Poco::Net::HTTPClientSession if it is misused
    (e.g., sendRequest() is sent two times in a row without an intermediate call to
    receiveResponse(), or by calling receiveResponse() two times in a row without
    an intermediate call to sendRequest()) - GH #217
  - removed a few unnecessary protected accessor methods from Poco::Net::HTTPClientSession
    that would provide inappropriate access to internal state
  - merged GH #210: Don't call CloseHandle() twice on Windows; Ability to select the
    threadpool that will be used to start an Activity(Patrice Tarabbia)
  - fixed GH #212: JSONConfiguration was missing from the vs90 project(Patrice Tarabbia)
  - fixed GH #220: add qualifiers for FPEnvironment in C99 (Lucas Clemente)
  - fixed GH #222: HTTPCookie doesn't support expiry times in the past (Karl Reid)
  - fixed GH #224: building 1.5.1 on Windows for x64
  - fixed GH #233: ServerSocket::bind6(Poco::UInt16 port, bool reuseAddress, bool ipV6Only) does not work
  - fixed GH #231: Compatibility issue with Poco::Net::NetworkInterface
  - fixed GH #236: Bug in RecursiveDirectoryIterator
  - added ColorConsoleChannel and WindowsColorConsoleChannel classes supporting
    colorizing log messages
  - fixed GH #259: Poco::EventLogChannel fails to find 64bit Poco Foundation dll
  - fixed GH #254: UTF8::icompare unexpected behavior
  - Poco::UUID::tryParse() also accepts UUIDs without hyphens. Also updated documentation
    (links to specifications).
  - added GH #268: Method to get JSON object value using Poco::Nullable
  - fixed GH #267: JSON 'find' not returning empty result if object is expected but another
    value is found
  - Added support for ARM64 architecture and iPhone 5s 64-bit builds
    (POCO_TARGET_OSARCH=arm64).


!!Incompatible Changes and Possible Transition Issues

  - Dynamic::Var: comparison of two empty objects now returns true
  - WinCE does not build in this release; this will be fixed in a later release
  - JSON::DefaultHandler was renamed to a more appropriate name - ParseHandler;
    ParseHandler does not have to be passed to the Parser, it will be used by default;
    handlers are now passed into Parser as smart pointers, so passing in addresses of
    stack objects will cause undefined behavior
  - Please note that 1.5.x releases are development releases and not considered stable.
    Interfaces may change, and backwards compatibility with the stable 1.4 release
    series is not guaranteed. There may also be some rough edges.
    The next stable release incorporating 1.5 features will be 1.6.


!!!Release 1.5.1

!!Summary of Changes

  - using double-conversion library for floating-point numeric/string conversions
  - added Poco::istring (case-insensitive string) and Poco::isubstr (case-insensitive substring)
  - added Poco::Data::SQLite sys.dual (in-memory system table)
  - applied SF Patch #120: The ExpireLRUCache does not compile with a tuple as key on Visual Studio 2010
  - fixed SF Bug #599: Poco::JSON::Array and JSON::Object size() member can implicitly lose precision
  - fixed SF Bug #602: iterating database table rows not correct if no data in table
  - fixed SF Bug #603: count() is missing in HashMap
  - fixed GH #23: Poco::JSON::Object::stringify throw BadCastException
  - fixed GH #16: Poco::Net::NetworkInterface::firstAddress() should not throw on unconfigured interfaces
  - Android compile/build support (Rangel Reale)
  - improved iPhone compile/build (Rangel Reale)
  - TypeHandler::prepare() now takes const-reference
  - fixed GH #27: Poco::URI::decode() doesn't properly handle '+'
  - fixed GH #31: Poco::JSON implementation bug
  - fixed SF #597: Configure script ignores cflags
  - fixed SF #593: Poco 1.5.0 on FreeBSD: cannot find -ldl
  - added SF #542: SocketAddress() needs port-only constructor
  - fixed SF #215: Wrong return type in SocketConnector.h
  - applied SF Patch #97: fix c++0x / clang++ bugs
  - fixed GH32/SF596: Poco::JSON: Parsing long integer (int64) value fails.
  - added Net ifconfig sample (contributed by Philip Prindeville)
  - merged GH #34: add algorithm header (Roger Meier/Philip Prindeville)
  - improved CMake build (Mathaus Mendel)
  - fixed GH #26: Cannot compile on gcc
  - merged SF #111: FTP Client logging (Marian Krivos)
  - fixed GH #30: Poco::Path::home() throws when called from Windows Service
  - fixed GH #22: Poco::Data::MySQL connection string lowercased
  - added MySQL support for Date/Time
  - upgraded SQLite to version ******** (2012-12-19)
  - improved SQLite execute() return (affected rows) value and added tests
  - added bool Poco::Data::SQLite::Utility::isThreadSafe() function
  - added bool Poco::Data::SQLite::Utility::setThreadMode(int) function
  - added int Poco::Data::SQLite::Utility::getThreadMode() function
  - fixed GH #36: 'distclean' requires 3 traversals of project tree
  - fixed GH #41: Buffer::resize crash
  - fixed GH #42: Linux unbundled builds don't link
  - fixed GH #44: Problems with win x64 build
  - fixed GH #46: 1.5.1 build fails on OS X when using libc++
  - fixed GH #48: Need getArgs() accessor to Util::Application to retrieve start-up arguments
  - fixed GH #49: NetworkInterface::list doesn't return MAC addresses
  - fixed GH #51: Android should use isfinite, isinf, isnan and signbit from the std namespace
  - fixed GH #53: JSON unicode fixes and running tests on invalid unicode JSON
  - added ParallelAcceptor and ParallelReactor classes
  - added EOF and error to FIFOBuffer

!!Incompatible Changes and Possible Transition Issues

  - Please note that 1.5.x releases are development releases and not considered stable. Interfaces may
    change, and backwards compatibility with the stable 1.4 release series
    is not guaranteed. There may also be some rough edges.
    The next stable release incorporating 1.5 features will be 1.6.


!!!Release 1.5.0

!!Summary of Changes

  - added JSON library
  - added Util::JSONConfiguration
  - added FIFOBuffer and FIFOBufferStream
  - fixed SF# 3522906: Unregistering handlers from SocketReactor
  - fixed SF# 3522084: AbstractConfiguration does not support 64-bit integers
  - HTTPServer::stopAll(): close the socket instead of just shutting it down, as the latter won't wake up a select() on Windows
  - added SMTPLogger
  - added cmake support
  - fixed SF#3538778: NetworkInterface enumeration uses deprecated API
  - fixed SF#3538779: IPAddress lacks useful constructors: from prefix mask, native SOCKADDR
  - fixed SF#3538780: SocketAddress needs operator < function
  - fixed SF#3538775: Issues building on Fedora/Centos, etc. for AMD64
  - fixed SF#3538786: Use size_t for describing data-blocks in DigestEngine
  - added IPAddress bitwise operators (&,|,^,~)
  - added IPAddress BinaryReader/Writer << and >> operators
  - modified IPAddress to force IPv6 to lowercase (RFC 5952)
  - fixed SF#3538785: SMTPClientSession::sendMessage() should take recipient list
  - added IPAddress::prefixLength()
  - UTF portability improvements
  - fixed SF#3556186: Linux shouldn't use <net/if.h> in Net/SocketDefs.h
  - added IPAddress RFC 4291 compatible site-local prefix support
  - fixed SF#3012166: IPv6 patch
  - added SF#3558085: Add formatter to MACAddress object
  - fixed SF#3552774: Don't hide default target in subordinate makefile
  - fixed SF#3534307: Building IPv6 for Linux by default
  - fixed SF#3516844: poco missing symbols with external >=lipcre-8.13
  - added SF#3544720: AbstractConfigurator to support 64bit values
  - fixed SF#3522081: WinRegistryConfiguration unable to read REG_QWORD values
  - fixed SF#3563626: For Win32 set Up/Running flags on NetworkInterface
  - fixed SF#3560807: Deprecate setPeerAddress() as this is now done in getifaddrs
  - fixed SF#3560776: Fix byte-ordering issues with INADDR_* literals
  - fixed SF#3563627: Set IP address on multicast socket from socket family
  - fixed SF#3563999: Size BinaryWriter based on buffer's capacity(), not size()
  - fixed SF#102 Fix building Poco on Debian GNU/FreeBSD
  - fixed SF#321 Binding DatTime or Timestamp
  - fixed SF#307 Detect the SQL driver type at run time
  - added VS 2012 Projects/Solutions
  - enhanced and accelerated numeric parsing for integers and floats
  - fixed SF#590 Segfault on FreeBSD when stack size not rounded
  - added warn function and warnmsg macro in CppUnit
  - fixed SF# 3558012 Compilation fails when building with -ansi or -std=c++0x
  - fixed SF# 3563517 Get rid of loss-of-precision warnings on x64 MacOS
  - fixed SF#3562244: Portability fix for AF_LINK
  - fixed SF #3562400: DatagramSocketImpl comment is incorrect

!!Incompatible Changes and Possible Transition Issues

  - Keywords for the Data library (now, use, into, etc.) now reside in Poco::Data::Keywords namespace.
  - Please note that 1.5.x releases are development releases and not considered stable. Interfaces may
    change, and backwards compatibility with the stable 1.4 release series
    is not guaranteed. There may also be some rough edges.
    The next stable release incorporating 1.5 features will be 1.6.


!!!Release 1.4.7p1

!!Summary of Changes

  - Fixed Visual C++ 2010-2013 project files. Release builds now have optimization enabled.
  - Poco::URI: added constructor to create URI from Path.
  - fixed GH #618: OS X 10.10 defines PAGE_SIZE macro, conflicts with PAGE_SIZE in Thread_POSIX.cpp
  - Poco::Net::HTTPClientSession: added support for global proxy configuration
  - fixed GH #331: Poco::Zip does not support files with .. in the name.
  - fixed a memory leak in Poco::Net::Context constructor when it fails to load the certificate
    or private key files.
  - upgraded bundled SQLite to *******
  - fixed GH #229: added missing value() function
  - fixed GH #69: MySQL empty text/blob


!!!Release 1.4.7

!!Summary of Changes

  - fixed GH #398: PropertyFileConfiguration: input != output
  - fixed GH #368: Build failure of Poco 1.4.6p2 on FreeBSD 9.2
  - fixed GH #318: Logger local time doesn't automatically account for DST
  - fixed GH #317: Poco::Zip does not support newer Zip file versions.
  - fixed GH #454: Fix: handle unhandled exceptions
  - fixed GH #463: XML does not compile with XML_UNICODE_WCHAR_T
  - fixed GH #282: Using Thread in a global can cause crash on Windows
  - fixed GH #424: Poco::Timer deadlock
  - fixed GH #465: Fix result enum type XML_Error -> XML_Status
  - fixed GH #510: Incorrect RSAKey construction from istream
  - fixed GH #332: POCO::ConsoleChannnel::initColors() assigns no color to
    PRIO_TRACE and wrong color to PRIO_FATAL
  - fixed GH #550: WebSocket fragmented message problem
  - Poco::Data::MySQL: added SQLite thread cleanup handler
  - Poco::Net::X509Certificate: improved and fixed domain name verification for
    wildcard domains
  - fixed a crash in Foundation testsuite with Visual C++ 2012
  - improved and fixed domain name verification for wildcard domains in
    Poco::Net::X509Certificate
  - updated TwitterClient sample to use new 1.1 API and OAuth
  - added Poco::Clock class, which uses a system-provided monotonic clock
    (if available) and is thus not affected by system realtime clock changes.
    Monotonic Clock is available on Windows, Linux, OS X and on POSIX platforms
    supporting clock_gettime() and CLOCK_MONOTONIC.
  - Poco::Timer, Poco::Stopwatch, Poco::TimedNotificationQueue and Poco::Util::Timer
    have been changed to use Poco::Clock instead of Poco::Timestamp and are now
    unaffected by system realtime clock changes.
  - added Poco::PBKDF2Engine class template
  - Poco::Net::HTTPCookie: added support for Priority attribute (backport from develop)
  - fixed makedepend.* scripts to work in paths containing '.o*'
    (contributed by Per-Erik Bjorkstad, Hakan Bengtsen)
  - Upgraded bundled SQLite to 3.8.6
  - Support for Windows Embedded Compact 2013 (Visual Studio 2012)
  - Project and solution files for Visual Studio 2013
  - Changes for C++11 compatibility.
  - fixed an issue with receiving empty web socket frames (such as ping)
  - improved error handling in secure socket classes
  - Poco::ByteOrder now uses intrinsics if available
  - added new text encoding classes: Latin2Encoding, Windows1250Encoding, Windows1251Encoding
  - Zip: Added CM_AUTO, which automatically selects CM_STORE or CM_DEFLATE based on file extension.
    Used to avoid double-compression of already compressed file formats such as images.


!!!Release 1.4.6p4

!!Summary of Changes

  - no longer use reverse DNS lookups for cert hostname validation
  - cert hostname validation is case insensitive and more strict
  - HTMLForm: in URL encoding, percent-encode more special characters
  - fixed thread priority issues on POSIX platforms with non-standard scheduling policy
  - XMLWriter no longer escapes apostrophe character
  - fixed GH #316: Poco::DateTimeFormatter::append() gives wrong result for Poco::LocalDateTime
  - fixed GH #305 (memcpy in Poco::Buffer uses wrong size if type != char)
  - Zip: fixed a crash caused by an I/O error (e.g., full disk) while creating a Zip archive


!!!Release 1.4.6p3

!!Summary of Changes

  - Fixed a potential security vulnerability in client-side X509
    certificate verification.


!!!Release 1.4.6p2

!!Summary of Changes

  - fixed GH #156: Possible buffer overrun in Foundation/EventLogChannel
  - XML: fixed an issue with parsing a memory buffer > 2 GB
  - upgraded to expat 2.1.0
  - Data/ODBC: added support for setting query timeout (via setProperty
    of "queryTimeout"). Timeout is int, given in seconds.
  - fixed a potential endless loop in SecureStreamSocketImpl::sendBytes()
    and also removed unnecessary code.
  - fixed GH #159: Crash in openssl CRYPTO_thread_id() after library libPocoCrypto.so
    has been unloaded.
  - fixed GH #155: MailOutputStream mangles consecutive newline sequences
  - fixed GH #139: FileChannel::PROP_FLUSH is invalid (contains a tab character)
  - fixed GH #173: HTTPClientSession::proxyConnect forces DNS lookup of host names
  - fixed GH #194: MessageNotification constructor is inefficient.
  - fixed GH #189: Poco::NumberParser::tryParse() documentation bug
  - fixed GH #172: IPv6 Host field is stripped of Brackets in HTTPClientSession
  - fixed GH #188: Net: SocketAddress operator < unusable for std::map key
  - fixed GH #128: DOMWriter incorrectly adds SYSTEM keyword to DTD if PUBLIC is
    already specified
  - fixed GH #65: Poco::format() misorders sign and padding specifiers
  - upgraded bundled SQLite to 3.7.17
  - upgraded bundled zlib to 1.2.8
  - fixed a potential memory leak in Poco::Net::HTTPClientSession if it is misused
    (e.g., sendRequest() is sent two times in a row without an intermediate call to
    receiveResponse(), or by calling receiveResponse() two times in a row without
    an intermediate call to sendRequest()) - GH #217
  - removed a few unnecessary protected accessor methods from Poco::Net::HTTPClientSession
    that would provide inappropriate access to internal state
  - fixed GH #223 (Poco::Net::HTTPCookie does not support expiry times in the past)
  - fixed GH #233: ServerSocket::bind6(Poco::UInt16 port, bool reuseAddress, bool ipV6Only)
    does not work
  - added ColorConsoleChannel and WindowsColorConsoleChannel classes supporting
    colorizing log messages
  - fixed GH #259: Poco::EventLogChannel fails to find 64bit Poco Foundation dll
  - fixed GH #254: UTF8::icompare unexpected behavior
  - Poco::UUID::tryParse() also accepts UUIDs without hyphens. Also updated documentation
    (links to specifications).
  - Added support for ARM64 architecture and iPhone 5s 64-bit builds
    (POCO_TARGET_OSARCH=arm64).


!!!Release 1.4.6p1

!!Summary of Changes

  - fixed GH #71: WebSocket and broken Timeouts (POCO_BROKEN_TIMEOUTS)
  - fixed an ambiguity error with VC++ 2010 in Data/MySQL testsuite
  - Poco::Net::NetworkInterface now provides the interface index even for IPv4
  - added DNS::reload() as a wrapper for res_init().
  - On Linux, Poco::Environment::nodeId() first always tries to obtain the
    MAC address of eth0, before looking for other interfaces.
  - Poco::Net::HTTPSession now always resets the buffer in connect() to clear
    any leftover data from a (failed) previous session
  - fixed copysign namespace issue in FPEnvironment_DUMMY.h
  - fixed a warning in Poco/Crypto/OpenSSLInitializer.h
  - added a build configuration for BeagleBoard/Angstrom
  - fixed GH #109: Bug in Poco::Net::SMTPClientSession::loginUsingPlain)
  - fixed compile errors with clang -std=c++11
  - fixed GH #116: Wrong timezone parsing in DateTimeParse (fix by Matej Knopp)
  - updated bundled SQLite to ********


!!!Release 1.4.6

!!Summary of Changes

  - changed FPEnvironment_DUMMY.h to include <cmath> instead of <math.h>
  - updated bundled SQLite to ********
  - fixed GH #30: Poco::Path::home() throws
  - fixed SF Patch# 120: The ExpireLRUCache does not compile with a tuple as key on VS2010
  - fixed SF# 603: count() is missing in HashMap
  - Crypto and NetSSL_OpenSSL project files now use OpenSSL *MD.lib library files for
    static_md builds. Previously, the DLL import libs were used.
  - Poco::Environment::osDisplayName() now recognizes Windows 8/Server 2012


!!!Release 1.4.5

!!Summary of Changes

  - added Visual Studio 2012 project files
  - buildwin.cmd now support building with msbuild for VS2010 and 2012.
  - added Poco::Optional class
  - fixed SF# 3558012 Compilation fails when building with -ansi or -std=c++0x
  - fixed SF# 3563517 Get rid of loss-of-precision warnings on x64 MacOS
  - fixed SF# 3562244: Portability fix for AF_LINK
  - fixed SF# 3562400: DatagramSocketImpl comment
  - fixed SF# 594: Websocket fails with small masked payloads
  - fixed SF# 588: Missing POCO_ARCH and POCO_ARCH_LITTLE_ENDIAN define for WinCE on SH4
  - fixed SF# 581: Out-of-bound array access in Unicode::properties() function.
  - fixed SF# 590: Segfault on FreeBSD when stack size not rounded
  - fixed SF# 586: Poco::DateTimeParser and ISO8601 issues when seconds fraction has more than 6 digits
  - Poco::Net::HTTPSSessionInstantiator::registerInstantiator() now optionally accepts a
    Poco::Net::Context object.
  - added Poco::XML::XMLWriter::depth() member function.
  - added Poco::XML::XMLWriter::uniquePrefix() and Poco::XML::XMLWriter::isNamespaceMapped().
  - Poco::FileChannel now supports a new rotateOnOpen property (true/false) which can be used
    to force rotation of the log file when it's opened.
  - fixed a bug in Poco::XML::XMLWriter::emptyElement(): need to pop namespace context
  - OS X builds now use Clang as default compiler
  - Updated SQLite to ********
  - POCO_SERVER_MAIN macro now has a try ... catch block for Poco::Exception and writes
    the displayText to stderr.
  - Poco/Platform.h now defines POCO_LOCAL_STATIC_INIT_IS_THREADSAFE macro if the compiler
    generates thread-safe static local initialization code.


!!!Release 1.4.4

!!Summary of Changes

  - ZipStream now builds correctly in unbundled build.
  - added proxy digest authentication support to Net library
  - integrated MySQL BLOB fixes from Franky Braem.
  - use standard OpenSSL import libraries (libeay32.lib, ssleay32.lib) for Crypto and
    NetSSL_OpenSSL Visual Studio project files.
  - fixed a potential buffer corruption issue in Poco::Net::SecureStreamSocket if lazy
    handshake is enabled and the first attempt to complete the handshake fails
  - Poco::DateTimeParser::tryParse() without format specifier now correctly parses ISO8601
    date/times with fractional seconds.
  - Poco::Process::launch() now has additional overloads allowing to specify an initial
    directory and/or environment.
  - Poco::Net::FTPClientSession: timeout was not applied to data connection, only to
    control connection.
  - Fixed potential IPv6 issue with socket constructors if IPv6 SocketAddress is given
    (contributed by ??????? ????????? <<EMAIL>>).
  - Added an additional (optional) parameter to Poco::Thread::setOSPriority() allowing to
    specify a scheduling policy. Currently this is only used on POSIX platforms and allows
    specifying SCHED_OTHER (default), SCHED_FIFO or SCHED_RR, as well as other
    platform-specific policy values.
  - Added Poco::Crypto::DigestEngine class providing a Poco::DigestEngine interface to
    the digest algorithms provided by OpenSSL.
  - Fixed some potential compiler warnings in Crypto library
  - In some cases, when an SSL exception was unexpectedly closed, a generic Poco::IOException
    was thrown. This was fixed to throw a SSLConnectionUnexpectedlyClosedException instead.
  - Added Poco::ObjectPool class template.
  - Poco::Net::HTTPServer has a new stopAll() method allowing stopping/aborting of all
    currently active client connections.
  - The HTTP server framework now actively prevents sending a message body in the
    response to a HEAD request, or in case of a 204 No Content or 304 Not Modified
    response status.
  - fixed a DOM parser performance bug (patch by Peter Klotz)
  - fixed SF# 3559325: Util Windows broken in non-Unicode
  - updated iOS build configuration to use xcode-select for finding toolchain
  - Poco::Net::SecureSocketImpl::shutdown() now also shuts down the underlying socket.
  - fixed SF# 3552597: Crypto  des-ecb error
  - fixed SF# 3550553: SecureSocketImpl::connect hangs
  - fixed SF# 3543047: Poco::Timer bug for long startInterval/periodic interval
  - fixed SF# 3539695: Thread attributes should be destroyed using the pthread_attr_destroy()
  - fixed SF# 3532311: Not able to set socket option on ServerSocket before bind
    Added Poco::Net::Socket::init(int af) which can be used to explicitely
    initialize the underlying socket before calling bind(), connect(), etc.
  - fixed SF# 3521347: Typo in UnWindows.h undef
  - fixed SF# 3519474: WinRegistryConfiguration bug
    Also added tests and fixed another potential issue with an empty root path passed to the constructor.
  - fixed SF# 3516827: wrong return value of WinRegistryKey::exists()
  - fixed SF# 3515284: RSA publickey format(X.509 SubjectPublicKeyInfo)
  - fixed SF# 3503267: VxWorks OS prio is not set in standard constructor
  - fixed SF# 3500438: HTTPResponse failure when reason is empty
  - fixed SF# 3495656: numberformater, numberparser error in mingw
  - fixed SF# 3496493: Reference counting broken in TaskManager postNotification
  - fixed SF# 3483174: LogFile flushing behavior on Windows
    Flushing is now configurable for FileChannel and SimpleFileChannel
    using the "flush" property (true or false).
  - fixed SF# 3479561: Subsequent IPs on a NIC is not enumerated
  - fixed SF# 3478665: Permission checks in Poco::File not correct for root
  - fixed SF# 3475050: Threading bug in initializeNetwork() on Windows
  - fixed SF# 3552680: websocket small frames bug and proposed fix
  - fixed a WebSocket interop issue with Firefox
  - added Poco::Net::MessageHeader::hasToken()
  - Poco::AtomicCounter now uses GCC 4.3 builtin atomics on more platforms
  - fixed SF# 3555938: NetSSL: socket closed twice
  - socket exceptions now include OS error code
  - fixed SF# 3556975: Need to fix Shared Memory for memory map
  - Poco::Net::SecureSocketImpl::close() now catches exceptions thrown by its call to shutdown().
  - fixed SF# 3535990: POCO_HAVE_IPv6 without POCO_WIN32_UTF8 conflict
  - fixed SF# 3559665: Poco::InflatingInputStream may not always inflate completely
  - added Poco::DirectoryWatcher class
  - fixed SF# 3561464: Poco::File::isDevice() can throw due to sharing violation
  - Poco::Zip::Compress::addRecursive() has a second variant that allows to specify the compression method.
  - Upgraded internal SQLite to 3.7.14


!!!Release 1.4.3p1

!!Summary of Changes

  - fixed SF# 3476926: RegDeleteKeyEx not available on Windows XP 32-bit.


!!!Release 1.4.3

!!Summary of Changes

  - fixed a compilation error with Data/MySQL on QNX.
  - fixed Util project files for WinCE (removed sources not compileable on CE)
  - removed MD2 license text from Ackowledgements document
  - fixed iPhone build config for Xcode 4.2 (compiler name changed to llvm-g++)
  - Poco::Util::XMLConfiguration: delimiter char (default '.') is now configurable.
    This allows for working with XML documents having element names with '.' in them.
  - Poco::Util::OptionProcessor: Required option arguments can now be specified as
    separate command line arguments, as in "--option value" in addition to the
    "--option=value" format.
  - Poco::Util::HelpFormatter: improved option help formatting if  indentation has
    been set explicitely.
  - added Mail sample to NetSSL_OpenSSL, showing use of Poco::Net::SecureSMTPClientSession.
  - added additional read() overloads to Poco::Net::HTMLForm.
  - fixed SF# 3440769: Poco::Net::HTTPResponse doesn't like Amazon EC2 cookies.
  - added support for requiring TLSv1 to Poco::Net::Context.
  - added an additional constructor to Poco::Net::HTTPBasicCredentials, allowing
    the object to be created from a string containing a base64-encoded, colon-separated
    username and password.
  - Poco::Zip::ZipStreamBuf: fixed a crash if CM_STORE was used.
  - Added setContentLength64() and getContentLength64() to Poco::Net::HTTPMessage.
  - added Poco::Environment::osDisplayName().
  - fixed SF# 3463096: WinService leaves dangling handles (open() now does not reopen the
    service handle if it's already open)
  - fixed SF# 3426537: WinRegistryConfiguration can't read virtualized keys
  - added Poco::Buffer::resize()
  - fixed SF# 3441822: thread safety issue in Poco::Net::HTTPClientSession:
    always use getaddrinfo() instead of gethostbyname() on all platforms supporting it
  - added version resource to POCO DLLs
  - fixed SF# 3440599: Dir Path in Quotes in PATH cause PathTest::testFind to fail.
  - fixed SF# 3406030: Poco::Glob::collect() problem
  - added Poco::Util::AbstractConfiguration::enableEvents()
  - Poco::AtomicCounter now uses GCC builtins with GCC 4.1 or newer
    (contributed by Alexey Milovidov)
  - made Poco::Logger::formatDump() public as it may be useful for others as well
    (SF# 3453446)
  - Poco::Net::DialogSocket now has a proper copy constructor (SF# 3414602)
  - Poco::Net::MessageHeader and Poco::Net::HTMLForm now limit the maximum number of
    fields parsed from a message to prevent certain kinds of denial-of-service
    attacks. The field limit can be changed with the new method setFieldLimit().
    The default limit is 100.
  - Poco::NumberFormatter, Poco::NumberParser and Poco::format() now always use the
    classic ("C") locale to format and parse floating-point numbers.
  - added Poco::StreamCopier::copyStream64(), Poco::StreamCopier::copyStreamUnbuffered64()
    and Poco::StreamCopier::copyToString64(). These functions use a 64-bit integer
    to count the number of bytes copied.
  - upgraded internal zlib to 1.2.5
  - upgraded internal sqlite to 3.7.9
  - XML: integrated bugfix for Expat bug# 2958794 (memory leak in poolGrow)
  - Added support for HTTP Digest authentication (based on a contribution by
    Anton V. Yabchinskiy (arn at bestmx dot ru)). For information on how
    to use this, see the Poco::Net::HTTPCredentials, Poco::Net::HTTPDigestCredentials
    and Poco::Net::HTTPAuthenticationParams classes.
  - Poco::Net::HTTPStreamFactory and Poco::Net::HTTPSStreamFactory now support Basic
    and Digest authentication. Username and password must be provided in the URI.
  - added Poco::Net::WebSocket, supporting the WebSocket protocol as described in RFC 6455
  - NetSSL_OpenSSL: added client-side support for Server Name Indication.
    Poco::Net::SecureSocketImpl::connectSSL() now calls SSL_set_tlsext_host_name()
    if its available (OpenSSL 9.8.6f and later).
  - added Poco::Net::HTTPClientSession::proxyConnect() (factored out from
    Poco::Net::HTTPSClientSession::connect())
  - added Poco::Process::kill(const Poco::ProcessHandle&) which is preferable to
    kill(pid) on Windows, as process IDs on Windows may be reused.
  - fixed SF# 3471463: Compiler warnings with -Wformat
  - Poco::Util::Application::run() now catches and logs exceptions thrown in initialize()
  - Fixed a WinCE-specific bug in Poco::Util::ServerApplication where uninitialize() would
    be called twice.
  - fixed SF# 3471957: WinRegistryKey::deleteKey() unable to delete alt views
  - Added additional constructor to Poco::ScopedLock and Poco::ScopedLockWithUnlock
    accepting a timeout as second argument.
  - Added Poco::Logger::parseLevel()
  - Poco::format(): an argument that does not match the format
    specifier no longer results in a BadCastException. The string [ERRFMT] is
    written to the result string instead.


!!!Release 1.4.2p1

!!Summary of Changes

  - On Linux, the RTLD_DEEPBIND option is no longer passed to dlopen().
    This change was introduced in 1.4.2 to solve a specific problem one customer
    was having. Unfortunately, it leads to problems with RTTI.
  - It's now possible to pass flags (SHLIB_GLOBAL, SHLIB_LOCAL) to
    Poco::SharedLibrary::load() (and the constructor implicitly calling load()),
    controlling the mode flags (RTLD_GLOBAL, RTLD_LOCAL) passed to dlopen().
    On platforms not using dlopen(), these flags are ignored.
  - fixed SF# 3400267: Path_WIN32.cpp bug


!!!Release 1.4.2

!!Summary of Changes

  - added Poco::DateTimeFormat::ISO8601_FRAC_FORMAT
  - added new Poco::DateTimeFormatter and Poco::DateTimeParser format specifier:
    %s for seconds with optional fractions of a second
  - fixed a problem with ioctl() on BSD platforms (including OS X) where the
    second argument to ioctl() is unsigned long instead of int, causing bad
    things on a OS X 64-bit kernel.
  - fixed a potential endless loop when enumerating IPv6 network addresses
    (reported by Laurent Carcagno)
  - new compile-time config option on Windows to set thread names in
    debugger. Enable with -DPOCO_WIN32_DEBUGGER_THREAD_NAMES. Available
    only in debug builds.
  - Cipher can now create Base64 and HexBinary encoded output without linefeeds
    (suitable for use in cookies, etc.)
  - added Poco::Path::popFrontDirectory()
  - improved VxWorks support
  - IPv6 fixes: added proper scope id handling in IPAddress, SocketAddress
    and related classes.
  - Added Poco::Net::ServerSocket::bind6() which allows control over the
    IPPROTO_IPV6/IPV6_V6ONLY socket option.
  - Removed Poco::MD2Engine class due to licensing issues (the
    license for the MD2 code from RSA only allows non-commercial
    use). Note that the MD4 and MD5 code from RSA does not have
    this issue.
  - fixed a Net HTTP client testsuite issue where some tests might
    have failed due to prematurely aborted connections by
    the HTTPTestServer.
  - Poco::Net::SocketAddress: when there is more than one address
    returned by a DNS lookup for a name, IPv4 addresses will be
    preferred to IPv6 ones.
  - Poco::Net::NetworkInterface::list() now also returns IPv4 interfaces on Windows when
    built with -DPOCO_HAVE_IPv6
  - Poco::XML::XMLWriter: fixed a bug with attribute namespaces (no namespace prefix
    written if attribute namespace is the same as element namespace)
  - fixed SF# 3378588: Mismatched new[]/delete (in RSAEncryptImpl and RSADecryptImpl)
  - fixed SF# 3212954 (OpenSSLInitializer::uninitialize() crash) and
    SF# 3196862 (Static OpenSSLInitializer instance causes Windows deadlocks) by
    removing the static Poco::Crypto::OpenSSLInitializer instance. Automatic OpenSSL
    initialization is now done through Poco::Crypto::Cipher, Poco::Crypto::CipherKey,
    Poco::Crypto::X509Certificate, Poco::Net::Context classes; however, it is still
    recommended to call Poco::Crypto::initializeCrypto() and
    Poco::Crypto::uninitializeCrypto() early at application startup, and late at
    shutdown respectively (or Poco::Net::initializeSSL()/Poco::Net::uninitializeSSL()
    if the NetSSL library is used) to avoid multiple full OpenSSL init/uninit cycles
    during application runtime.
  - Poco::Logger now also support a symbolic log level "none"
    (for use with setLevel()) that disables logging completely
    for that Logger (equivalent to setLevel(0)).
  - Added experimental Android support, using the existing gmake-based
    build system.
  - fixed SF# 3288584: DateTimeFormatter link error
  - fixed SF# 3187117: Typo in InflatingInputStream doc
  - fixed SF# 3309731: _WIN32_WCE comparison should be with 0x600 not 600
  - fixed SF# 3393026: RegularExpression.h identical enum value
  - fixed SF# 3274222: AtomicCounter's postfix operators aren't atomic on Windows
  - fixed SF# 3317177: Handle leak on windows
  - fixed SF# 3181882: Poco::URI::getPathEtc() double-encodes query
  - fixed SF# 3379935: Poco::ThreadPool Start Bug
  - fixed SF# 3354451: Poco::Format::parsePrec() never sets the precision to zero
  - fixed SF# 3387258: _MAX_PATH used but unknown in Path_WIN32
  - fixed a problem in Poco::Crypto::RSAKeyImpl where direct access to the RSA in a
    EVP_PKEY would no longer work in recent OpenSSL versions. Using EVP_PKEY_get1_RSA()
    fixes the issue.
  - added Poco::Crypto::EncryptingInputStream, Poco::Crypto::EncryptingOutputStream,
    Poco::Crypto::DecryptingInputStream and Poco::Crypto::DecryptingOutputStream.
  - fixed SF# 3148126: Poco::Net::HTTPSClientSession destructor throws an IOException
  - fixed SF# 3178098: Add constructor to Poco::TemporaryFile to specify directory
  - fixed SF# 3175310: Absolute path when device
  - fixed SF# 3301207: Guided tour example contradicts apidoc (API doc was wrong)
  - Poco::Net::HTTPMessage::setContentLength() and Poco::Net::HTTPMessage::getContentLength() now
    use std::streamsize instead of int. This enables 64-bit Content-Length support at least
    on 64-bit platforms.
  - fixed SF# 3177530: Poco::TemporaryFile::tempName() + glob bug on xp
  - fixed SF# 3177372: Poco::FileChannel documentation inconsistency
  - added %E format specifier to Poco::PattermFormatter (epoch time in seconds
    since midnight, January 1 1970)
  - On Windows, Poco::Util::ServerApplication now supports a /description command
    line argument for specifying a service description (together with /registerService)
  - added Poco::Util::WinService::setDescription() and
    Poco::Util::WinService::getDescription()
  - fixed SF# 3155477: Incorrect URI path handling
  - fixed SF# 3309736: Extended Exception macros to set default exception code
    new macro is named POCO_DECLARE_EXCEPTION_CODE
  - added getter functions for modulus and exponents to Poco::Crypto::RSAKey.
  - added Poco::Net::SocketAddress::operator == () and
    Poco::Net::SocketAddress::operator != ()
  - fixed SF# 3182746: IPAddress.cpp IPv6 bug on big-endian
  - fixed SF# 3196961: Unix daemon fails to loadConfiguration() if started from cwd
  - fixed SF# 3393700: NotificationCenter may call a removed observer and crash.
  - Reworked implementation of the events framework (Poco::BasicEvent and friends).
    The framework is now completely multithreading save (even in the case that
    an event subscriber object unsubscribes and is deleted while an event is
    being dispatched). Also, the restriction that any object can only register
    one delegate for each event has been removed. For most cases, dispatching
    events should be faster, as dispatching an event now needs less dynamic memory
    allocations.
  - fixed SF# 3178109: getNodeByPath() changes:
    getNodeByPath() and getNodeByPathNS() have been moved to Poco::XML::Node.
    Furthermore, when invoked on a Poco::XML::Document, the behavior has changed
    so that the document element is now included when traversing the path (previously,
    traversal would start at the document element, now it starts at the document).
    The path expression can now start with a double-slash, which results in a recursive
    search for the path's first element in the DOM tree.
  - fixed SF# 3382935: String data being truncated using ODBC, and
    SF# 2921813: Wrong implementation of the ODBC string binding


!!!Release 1.4.1p1

!!Summary of Changes

  - Poco::Mutex is now a recursive mutex again on Linux
    (this was caused by an unfortunate feature test for
    PTHREAD_MUTEX_RECURSIVE which did not work on Linux
    as PTHREAD_MUTEX_RECURSIVE is an enum value and not
    a macro)
  - Poco::Net::SecureSocketImpl::abort() now only shuts
    down the underlying socket connection and does not free
    the SSL object, due to multithreading issues.


!!!Release 1.4.1

!!Summary of Changes

  - fixed SF# 3150223: Poco::BinaryReader cannot read std::vector correctly
  - fixed SF# 3146326: Poco::SharedMemory issue
  - made Poco::Net::HTTPSession::abort() virtual
  - added Poco::Net::SecureStreamSocket::abort() to immediately close
    a SSL/TLS connection without performing an orderly SSL/TLS shutdown.
  - fixed SF# 3148126: Poco::Net::HTTPSClientSession destructor (!) throws an IOException.
    Added try/catch block to Poco::Net::SecureSocketImpl destructor.
  - added additional constructor to Poco::Net::HTTPSClientSession, taking
    both a socket and a session object.
  - Poco::Net::HTTPSession::abort() now also can be used with a
    Poco::Net::HTTPSClientSession.
  - fixed SF# 3148045: make clean and distclean issues
  - changed Data library names on Unix/Linux platforms to
    match the names on Windows (PocoSQLite -> PocoDataSQLite,
    PocoMySQL -> PocoDataMySQL, PocoODBC -> PocoDataODBC)
  - added additional options to configure script
  - added additional documentation to Poco::Net::HTTPClientSession
  - Poco::Net::HTTPClientSession::receiveResponse() closes the connection
    if an exception is thrown while reading the response header.
    This ensures that a new connection will be set up for the next request
    if persistent connections are used.
  - improved Poco::Net::MultipartDecoder performance by reading directly from streambuf
  - improved performance of Poco::Base64Encoder, Poco::Base64Decoder,
    Poco::HexBinaryEncoder and Poco::HexBinaryDecoder by working directly with the
    given stream's streambuf.
  - improved performance of MessageHeader::read() by reading directly from streambuf
    instead of istream.
  - it is now possible to specify additional MIME part header fields
    for a MIME part through the Poco::Net::PartSource class.
  - upgraded SQLite to release 3.7.4
  - added experimental VxWorks support for VxWorks 5.5.1/Tornado 2.2 and
    newer. Please see the VxWorks Platform Notes in the reference documentation
    for more information. Currently, the VxWorks is untested; full support
    will be available in release 1.4.2.
  - fixed SF# 3165918: Poco::DynamicAny fails to convert from string to float
  - fixed SF# 3165910: Poco::Net::MessageHeader does not accept HTTP conforming header
  - made Poco::Task::cancel() virtual so that tasks can implement custom
    cancellation behavior.
  - added optional argument to Poco::Util::WinRegistryKey constructor
    to specify additional flags (in addition to KEY_READ and KEY_WRITE)
    for the samDesired argument of RegOpenKeyEx() or RegCreateKeyEx().
  - improved Poco::BasicEvent::notify() performance by avoiding an unnecessary heap
    allocation.
  - added additional well-known port numbers to Poco::URI: rtsp, sip, sips, xmpp.
  - added Poco::Net::MediaType::matchesRange()
  - improved invalid socket handling: a Poco::Net::InvalidSocketException is
    now thrown instead of an assertion when an operation is attempted on a closed or
    otherwise uninitialized socket.


!!!Release 1.4.0

!!Summary of Changes

  - Poco::Net::SSLManager: documentation fixes, code cleanup
  - Poco::Net::SSLManager: renamed PrivateKeyPassPhrase event to PrivateKeyPassphraseRequired
  - added Poco::Net::HTTPServerRequestImpl::socket() to get access to the underlying socket
  - added Poco::Net::Socket::secure() to find out whether a given socket supports SSL/TLS
  - added Poco::Net::SecureStreamSocket::havePeerCertificate()
  - NetSSL: added support for turning off extended certificate validation (hostname matching)
  - fixed SF# 2941228: Poco::Net::ICMPClient::ping() issues on Mac OS X
  - fixed SF# 2941231: Poco::Net::ICMPEventArgs out of bounds array access
  - added PageCompiler sample
  - added missing newline at end of xmlparse.c
  - Poco::Glob can now be used with an empty pattern which will match nothing (patch from Kim Graesman)
  - added support for HTTP proxy authentication (Basic authentication only)
  - fixed SF# 2958959: Poco::XML::XMLWriter must encode CR, LF and TAB in attribute values as character entities.
  - Poco::Net::HTMLForm now supports PUT requests as well (see <http://pocoproject.org/forum/viewtopic.php?f=12&t=2163&p=3930#p3930>)
  - fixed SF# #2970521: Poco::FileOutputStream and file permissions.
    (also fixed in File class)
  - removed an unused (and wrong) default parameter from EventImpl constructor for WIN32.
  - added full support for session caching to NetSSL_OpenSSL
  - fixed SF# 2984454: Poco::Util::Timer::scheduleAtFixedRate() works incorrectly
  - fixed a bug in Poco::Util::Timer that could lead to high CPU load if
    the system clock is moved forward.
  - added "system.nodeId" property to Poco::Util::SystemConfiguration
  - added a note to Poco::Util::ServerApplication documentation regarding
    creation of threads
  - added Poco::Net::IPAddress::broadcast() and Poco::Net::IPAddress::wildcard() to
    create broadcast (***************) and wildcard (0.0.0.0) addresses.
  - fixed SF# 2916154: Poco::Net::IPAddress::isLoopback() only works for 127.0.0.1.
  - added build configuration for iPhone Simulator
  - GNU Make based build system provides new variables: POCO_HOST_BINDIR, POCO_HOST_BINPATH,
    POCO_HOST_LIBDIR, POCO_HOST_LIBPATH and POCO_TARGET_* equivalents.
  - Poco::Util::Application::initialize() and Poco::Util::Application::uninitialize() will now be called from within run().
    This solves various issues with uninitialize() not being called, or being called inappropriately
    from the Application destructor.
    Please note that this change will break applications that use the Application class,
    but only call init() and not run().
  - added /startup option to specify startup mode for Windows services (automatic or manual)
  - fixed SF# 2967354: SecureSocketImpl shutdown/close problem
  - fixed SF# 3006340: LinearHashTable grows even if key already exists
  - fixed a particularly nasty Windows error handling issue that manifested itself on WinCE:
    WSAGetLastError() would be called after a std::string was created. The string creation could result
    in a heap operation which called a Windows API to allocate memory. This would reset the
    GetLastError() error code. Since WSAGetLastError() is just an alias for GetLastError(), the actual
    error code from the socket operation would be lost.
  - upgraded SQLite to 3.7.3
  - added --header-prefix option to PageCompiler
  - fixed SF# 3003875: SQLite data binding is broken
  - fixed SF# 2993988: Issue with multiple calls to open()/close() on File*Stream
  - fixed SF# 2990256: Poco::Net::HTMLForm and file uploads
  - fixed SF# 2969227: Poco::DateTimeParser bug
  - fixed SF# 2966698: Socket connect with timeout issue
  - fixed SF# 2981041: Bind NULL to a query (patch supplied)
  - fixed SF# 2961419: Poco::UTF8Encoding::convert() doesn't work properly in DEBUG mode
  - fixed SF# 2957068: Timeout value not picked up by proxy in Poco::Net::HTTPSClientSession
  - fixed NetSSL_OpenSSL test runner for Poco::Util::Application class changes
  - Poco::AbstractEvent, Poco::AbstractCache and related classes now accept a Mutex class as additional template argument.
    Poco::NullMutex can be used if no synchronization is desired.
  - Added Poco::AbstractEvent::empty() to check whether an event has registered delegates.
  - Poco::URI now correctly handles IPv6 addresses.
  - Added Poco::Nullable class template.
  - Added Poco::NullMutex, a no-op mutex to be used as template argument for template classes
    taking a mutex policy argument.
  - Poco::XML::XMLWriter: fixed a namespace handling issue that occured with startPrefixMapping() and endPrefixMapping()
  - Poco::Net::Context now allows for loading certificates and private keys from Poco::Crypto::X509Certificate objects
    and Poco::Crypto::RSAKey objects.
  - Poco::Crypto::RSAKey no longer uses temporary files for stream operations. Memory buffers are used instead.
  - fixed SF# 2957865: added Poco::UUID::tryParse()
  - All Zip classes now use Poco::File[Input|Output]Stream instead of std::[i|o]fstream.
    UTF-8 filenames will now be handled correctly on Windows.
  - fixed SF# 2902029: zlib flush support (Z_SYNC_FLUSH)
  - added Poco::TextBufferIterator class
  - fixed SF# 2977249: Use epoll instead select under Linux
    Poco::Net::Socket::select() and Poco::Net::Socket::poll() will use epoll under Linux if the Net library is compiled
    with -DPOCO_HAVE_FD_EPOLL. This is the default for the Linux build configuration (but not for
    the various build configurations targeting embedded Linux platforms).
  - fixed SF# 2941664: Memory leak in Poco::DeflatingStream with zero-length streams (also fixed some other potential,
    but unlikely, memory leaks)
  - fixed SF# 2946457: added Poco::Net::RejectCertificateHandler
  - fixed SF# 2946621: Poco::Path bug with POCO_WIN32_UTF8
  - fixed SF# 2929805: Environment::nodeId() does not work if no eth0 device exists
  - Poco::Environment::nodeId() no longer throws if no hardware ethernet address can be determined.
    It returns an all-zero address instead.
  - Added additional classification functions to Poco::Unicode class; made classification functions inline.
  - added Poco::Ascii class for ASCII character classification.
    Methods of the Ascii class are now used instead of the
    standard library functions (std::isspace(), etc.) due to
    possible inconsistent results or assertions when the
    standard library functions are used with character codes
    outside the ASCII range.
  - Poco::Net::MailMessage: fixed a bug in StringPartHandler that resulted in incorrect handling of non-ASCII data if
    char is signed.
  - Improved Poco::Net::SMTPClientSession compatibility with various mail servers when using AUTH_LOGIN authentication.
  - Added CRAM-SHA1 support to Poco::Net::SMTPClientSession
  - Poco::Net::SMTPClientSession now also supports login with AUTH PLAIN.
  - Added Poco::Net::SecureSMTPClientSession class, supporting STARTTLS for secure SMTP connections.
  - fixed an issue with SharedMemory on POSIX systems, where a shared memory region would be deleted
    despite the server flag set to true (see http://pocoproject.org/forum/viewtopic.php?f=12&t=3494).
  - PageCompiler: added a new page context directive, to allow passing custom context objects to the
    request handler.
  - fixed Poco::Net::StreamSocketImpl::sendBytes() for non-blocking sockets
  - added Poco::Net::DialogSocket::receiveRawBytes(), which should be used instead of receiveBytes() due to internal
    buffering by DialogSocket.
  - Poco::XML::DOMParser: FEATURE_WHITESPACE has been renamed to FEATURE_FILTER_WHITESPACE (which now matches the underlying URI)
    and is now handled correctly (previously we did the exact reverse thing)
  - added Poco::Util::AbstractConfiguration::remove() to remove a configuration property; added removeRaw() implementations
    to all implementations (contributions by Daniel Hobi and Alexey Shults).
  - fixed NetSSL_OpenSSL compilation error on Windows with OpenSSL 1.0
  - Added optional FIPS mode support to NetSSL_OpenSSL (contributed by Lior Okman).
    If OpenSSL has been configured and built with FIPS support, then FIPS support can
    be enabled by calling Poco::Crypto::OpenSSLInitializer::enableFIPSMode(true); or
    by setting the fips property in the OpenSSL configuration to true (see Poco::Net::SSLManager
    for details).
  - fixed SF# 3031530: Ping and possible no timeout
  - added Poco::Net::SocketReactor::onBusy(), called whenever at least one notification will
    be dispatched.
  - fixed SF# 3034863: Compiler warning in Net/IPAddress.h with poco 1.3.2
  - added support for CRAM-SHA1 authentication to Poco::Net::SMTPClientSession
  - Poco::format(): arguments can now be addressed by their index, e.g. %[2]d
  - Poco::Util::Timer::cancel() now accepts an optional boolean argument.
    If true is passed, cancel() waits until the task queue has been purged.
    Otherwise, it returns immediately and works asynchronously, as before.
  - Poco::Net::HTTPServerResponse::redirect() now accepts an optional additional
    argument to specify the HTTP status code for the redirection.
  - fixed a warning (BinaryReader.cpp) and error (ThreadLocal.cpp) in Foundation when compiling with Visual Studio 2010
  - fixed a wrong exception in Poco::Net::POP3ClientSession
  - Poco::Net::FTPClientSession and Poco::Net::SMTPClientSession now set the error code in exceptions they throw
  - fixed a potential race condition with terminating a Windows service based on Poco::Util::ServerApplication
  - fixed a bug in global build configuration file: explicitly setting POCO_CONFIG did not work on Solaris platforms,
    as it was always overridden by the automatically determined configuration.
  - Added support for MinGW cross builds on Linux.
  - Changed location of statically linked build products in the gmake-based build system.
    Statically linked executables are now in bin/$(OSNAME)/$(OSARCH)/static and no longer
    have the _s suffix
  - The POCO_VERSION macro now is in its own header file, "Poco/Version.h". It is no longer
    available through "Poco/Foundation.h".
  - added Poco::Net::HTTPCookie::escape() and Poco::Net::HTTPCookie::unescape().
  - fixed SF# 3021173: Poco::Thread (POSIX) returns uninitialised value for OS priority
  - fixed SF# 3040870: Poco::ThreadPool has no function to get assigned name
  - fixed SF# 3044303: Can't use own config file on Solaris & OSARCH_64BITS ignored
  - fixed SF# 2943896: Poco::AsyncChannel::log() blocks
  - fixed a bug in Poco::Util::WinRegistryKey::getInt():
    The size variable passed to RegQueryValueExW() should be initialized to the size
    of the output buffer.
  - Added rudimentary support for compiling with Clang 2.0 (Xcode 4) on Mac OS X.
  - New build configurations for Mac OS X: Darwin32 and Darwin64 for explicit
    32-bit and 64-bit builds. Note that the default Darwin build configuration
    will build 64-bit on Snow Leopard and 32-bit on Leopard, but will always place
    build products in Darwin/i386. The new Darwin32 and Darwin64 configurations
    will use the correct directories.
  - fixed SF# 3051598: Bug in URL encoding
  - Poco::ThreadPool::stopAll() (and thus also the destructor) will now wait for each
    pooled thread to terminate before returning. This fixes an issue with creating
    and orderly shutting down a thread pool in a plugin. Previously, a pooled thread
    in a thread pool created by a dynamically loaded library might still be running
    when the plugin's shared library was unloaded, resulting in Bad Things happening.
    This can now no longer happen. As a downside, a pooled thread that fails to
    finish will block stopAll() and the destructor forever.
  - NetSSL_OpenSSL: for a Poco::Net::SecureStreamSocket, available() now returns the number of bytes that
    are pending in the SSL buffer (SSL_pending()), not the actual socket buffer.
  - Added Poco::Net::HTTPClientSession::secure() to check for a secure connection.
  - Poco::Net::HTTPRequest::setHost() now does not include the port number in the Host header
    if it's either 80 or 443.
  - log messages can now optionally include source file path and line number
  - Poco::PatternFormatter can format source file path and line number (%U, %u)
  - logging macros (poco_information(), etc.) now use __LINE__ and __FILE__
  - new logging macros that incorporate Poco::format(): poco_information_f1(logger, format, arg) with up to 4 arguments
  - added Poco::Net::HTTPSession::attachSessionData() and Poco::Net::HTTPSession::sessionData()
    to attach arbitrary data to a HTTP session.
  - added additional constructors to zlib stream classes that allow passing
    a windowBits parameter to the underlying zlib library.
  - fixed a potential error handling issue in Poco::Net::SecureSocketImpl.
  - fixed SF# 3110272: Poco::Crypto::RSACipherImpl bug.
  - fixed SF# 3081677: Poco::Util::ConfigurationView's getRaw not retrieving xml attributes.
  - added basic support for Canonical XML and better pretty-printing support to Poco::XML::XMLWriter.
  - Poco::Util::AbstractConfiguration now supports events fired when changing or
    removing properties.
  - XML: added support for finding DOM nodes by XPath-like
    expressions. Only a very minimal subset of XPath is supported.
    See Poco::XML::Element::getNodeByPath(), Poco::XML::Element::getNodeByPathNS()
    and the same methods in Poco::XML::Document.
  - Poco::Timer: If the callback takes longer to execute than the
    timer interval, the callback function will not be called until the next
    proper interval. The number of skipped invocations since the last
    invocation will be recorded and can be obtained by the callback
    by calling skipped().
  - Poco::BinaryReader and Poco::BinaryWriter now support reading and
    writing std::vectors of the supported basic types. Also, strings
    can now be written in a different encoding (a Poco::TextEncoding
    can be optionally passed to the constructor).
  - Windows Embedded CE (5.0 and newer) is now a supported platform.
  - Poco::UUID::nil() and Poco::UUID::isNil() have been renamed to
    Poco::UUID::null() and Poco::UUID::isNull(), respectively, to avoid
    issues with Objective-C++ projects on Mac OS X and iOS where nil is
    a system-provided macro.
  - Crypto bugfixes: Poco::Crypto::RSACipherImpl now pads every block of data, not just the
    last (or last two).
  - Improved Crypto testsuite by adding new tests.
  - Added new Visual Studio project configurations: debug_static_mt and release_static_mt
    (linking with static runtime libraries). The existing configurations debug_static
    and release_static have been renamed to debug_static_md and release_static_md, respectively.
    The suffixes of the static libraries have also changed. The static_md configurations
    now build libraries with suffixes md[d], while the libraries built by the static_mt
    configurations have mt[d] suffixes.
  - Added Visual Studio project files for 64-bit builds.
  - Added Visual Studio 2010 project files.
  - Removed the use of local static objects in various methods due to
    their construction not being threadsafe (and thus leading to
    potential race conditions) on Windows/Visual C++.
  - Fixed some warning on 64-bit Windows builds.
  - The names of the Data connector libraries have changed. They are now
    named PocoDataMySQL, PocoDataODBC and PocoDataSQLite.
  - fixed SF# 3125498: Linux NetworkInterface::list() doesn't return IPv6 IPs
  - fixed SF# 3125457: IPv6 IPAddress tests are wrong
  - Added initialization functions for the NetSSL_OpenSSL and Crypto libraries.
    These should be called instead of relying on automatic initialization,
    implemented with static initializer objects, as this won't work with
    statically linked executables (where the linker won't include the
    static initializer object).
    The functions are Poco::Crypto::initializeCrypto(), Poco::Crypto::uninitializeCrypto(),
    Poco::Net::initializeSSL() and Poco::Net::uninitializeSSL().
    Applications using Crypto and/or NetSSL should call these methods appropriately at
    program startup and shutdown.
    Note: In release 1.3.6, similar functions have been added to the Net library.


!!Incompatible Changes and Possible Transition Issues

  - The behavior of Poco::Timer has been changed for the case when
    the callback function takes longer to execute than the given
    timer interval. In this case, the missed intervals are counted
    and the next callback will be at the next proper interval.
  - Poco::XML::DOMParser: FEATURE_WHITESPACE has been renamed to FEATURE_FILTER_WHITESPACE
    (which now matches the underlying URI)
    and is now handled correctly (previously we did the exact reverse thing)
  - Poco::UUID::nil() and Poco::UUID::isNil() have been renamed to
    Poco::UUID::null() and Poco::UUID::isNull(), respectively, to avoid
    issues with Objective-C++ projects on Mac OS X and iOS where nil is
    a system-provided macro.
  - The names of the Data connector libraries have changed. They are now
    named PocoDataMySQL, PocoDataODBC and PocoDataSQLite.
  - The existing Visual Studio build configurations debug_static
    and release_static have been renamed to debug_static_md and
    release_static_md, respectively. Also, the suffixes of the static
    library names have changed to match usual conventions.
  - New static initialization functions for the Crypto and NetSSL_OpenSSL libraries
    have been added. These should be called instead of relying on automatic initialization,
    implemented with static initializer objects, as this won't work with
    statically linked executables (where the linker won't include the
    static initializer object).
    The functions are Poco::Crypto::initializeCrypto(), Poco::Crypto::uninitializeCrypto(),
    Poco::Net::initializeSSL() and Poco::Net::uninitializeSSL().
    Applications using Crypto and/or NetSSL should call these methods appropriately at
    program startup and shutdown.
    Note: In release 1.3.6, similar functions have been added to the Net library.


!!!Release 1.3.6p2

!!Summary of Changes

  - fixed an issue in the Windows implementation Poco::RWLock, where
    tryReadLock() sometimes would return false even if no writers
    were using the lock (fix contributed by Bjrn Carlsson)
  - added Poco::Environment::libraryVersion().
  - fixed SF# 2919461: Context ignores parameter cypherList
  - removed an unused enum from RSACipherImpl.cpp (Crypto)
  - integrated a new expat patch for CVE-2009-3560.
  - fixed SF# 2926458: SSL Context Problem. The Poco::Net::Context
    class now makes sure that OpenSSL is properly initialized.
  - updated iPhone build configuration (contributed by Martin York)
  - fixed SF# 1815124 (reopened): XML Compile failed on VS7.1 with
    XML_UNICODE_WCHAR_T
  - fixed SF# 2932647: FTPClientSession::getWorkingDirectory() returns a bad result


!!!Release 1.3.6p1

!!Summary of Changes

  - added support for using external zlib, pcre, expat and sqlite3 instead of
    bundled ones (-DPOCO_UNBUNDLED, configure --unbundled)
  - fixed SF# 2911407: Add sh4 support
  - fixed SF# 2912746: RSAKey::EXP_LARGE doesn't work
  - fixed SF# 2904119: abstractstrategy uses std::set but do not includes it
  - fixed SF# 2909946: localtime NULL pointer
  - fixed SF# 2914986: potential expat DoS security issues (CVE-2009-3560 and CVE-2009-3720)
  - fixed SF# 2916305: SSL Manager crashes
  - fixed SF# 2903676: Tuple TypeHander does not handle composites.


!!!Release 1.3.6

!!Summary of Changes

  - added Environment::processorCount()
  - added POCO_VERSION macro to Poco/Foundation.h
  - fixed SF# 2807527: Poco::Timer bug for long startInterval/periodic interval
  - fixed a bug similar to SF# 2807527 in Poco::Util::Timer.
  - fixed SF# 2795395: Constructor doesn't treat the params "key" and "iv"
  - fixed SF# 2804457: DateTime::checkLimit looks wrong
  - fixed SF# 2804546: DateTimeParser requires explicit RFC1123 format
  - added ReleaseArrayPolicy to Poco::SharedPtr
  - upgraded to SQLite 3.6.20
  - fixed SF# 2782709: Missing semicolons in "Logger.h" convenience
  - fixed SF# 2526407: DefaultStrategy.h ++it instead of it++ in a loop
  - fixed SF# 2502235: Poco STLPort patch
  - fixed SF# 2186643: Data::Statement::reset() not implemented in 1.3.3
  - fixed SF# 2164227: Allow File opened read only by FileInputSteam to be writable
  - fixed SF# 2791934: use of char_traits::copy in BufferedStreamBuf::underflow
  - fixed SF# 2807750: Support additional SQL types in SQLite
  - fixed documentation bugs in Timed/PriorityNotificationQueue
  - fixed SF# 2828401: Deadlock in SocketReactor/NotificationCenter (also fixes patch# 1956490)
    NotificationCenter now uses a std::vector internally instead of a std::list, and the mutex is
    no longer held while notifications are sent to observers.
  - fixed SF# 2835206: File_WIN32 not checking aganist INVALID_HANDLE_VALUE
  - fixed SF# 2841812: Posix ThreadImpl::sleepImpl throws exceptions on EINTR
  - fixed SF# 2839579: simple DoS for SSL TCPServer, HTTPS server
    No SSL handshake is performed during accept() - the handshake is delayed until
    sendBytes(), receiveBytes() or completeHandshake() is called for the first time.
    This also allows for better handshake and certificate validation when using
    nonblocking connections.
  - fixed SF# 2836049: Possible handle leak in FileStream
    If sync() fails, close() now simply set's the stream's bad bit.
    In any case, close() closes the file handle/descriptor.
  - fixed SF# 2814451: NetSSL: receiveBytes crashes if socket is closed
  - added a workaround for Vista service network initialization issue
    (an Windows service using the Net library running under Vista will
    crash in the call to WSAStartup() done in NetworkInitializer).
    Workaround is to call WSAStartup() in the application's main().
    Automatic call to WSAStartup() in the Net library can now be disabled
    by compiling Net with -DPOCO_NET_NO_AUTOMATIC_WSASTARTUP. Also
    the new Poco::Net::initializeNetwork() and Poco::Net::uninitializeNetwork()
    functions can be used to call WSAStartup() and WSACleanup(), respectively,
    in a platform-independent way (on platforms other than Windows, these
    functions will simply do nothing).
  - added VCexpress build script support (contributed by Jolyon Wright)
  - fixed SF# 2851052: Poco::DirectoryIterator copy constructor is broken
  - fixed SF# 2851197: IPAddress ctor throw keyword missing
  - added Poco::ProtocolException
  - PageCompiler improvements: new tags, support for buffered output, etc.
  - better error reporting in Data MySQL connector (patch #2881270 by Jan "HanzZ" Kaluza)
  - fixed SF# 1892462: FTPClient:Choose explicitely between EPSV and PASV
  - fixed SF# 2806365: Option for PageCompiler to write output to different dir
  - fixed a documentation bug (wrong sample code) in Process::launch() documentation
  - added --header-output-dir option to PageCompiler
  - fixed SF# 2849144: Zip::Decompress notifications error
  - SAXParser has a new feature: "http://www.appinf.com/features/enable-partial-reads".
    See ParserEngine::setEnablePartialReads() for a description of what this does.
  - fixed SF# 2876179: MySQL Signed/Unsigned value bug
  - fixed SF# 2877970: possible bug in timer task
  - fixed SF# 2874104: wrong parsing empty http headers
  - fixed SF# 2860694: Incorrect return code from SecureStreamSocketImpl::sendBytes
  - fixed SF# 2849750: Possible bug with XMLWriter?
  - added MailMessage::encodeWord() to support RFC 2047 word encoded
    mail header fields when sending out mail containing non-ASCII
    characters.
  - fixed SF# 2890975: SMTPClientSession bug with 7BIT encoding
  - fixed an issue with retrieving the value of socket options on Windows 7.
    Before obtaining the value of a socket, we now initialize the variable receiving the
    socket option value to zero.
  - fixed SF# 2836141: Documentation errors
  - fixed SF# 2864232: Socket::select() does not detect closed sockets on windows
  - fixed SF# 2812143: Socket::select() should check socket descriptors...
  - fixed SF# 2801750: NetworkInterface <iface-Obj>forName returns wrong subnetMask
  - fixed SF# 2816315: Problem with POSIX Thread::sleepImpl
  - fixed SF# 2795646: IPv6 address parsing bug
  - fixed #0000092: ServerApplication::waitForTerminationRequest(), SIGINT and GDB.
    Poco::Util::ServerApplication::waitForTerminationRequest() no longer registers a
    signal handler for SIGINT if the environment variable POCO_ENABLE_DEBUGGER
    is defined.
  - fixed SF# 2896070: Poco::Net::Context with non-ASCII paths
  - added Unicode Surrogate support to Poco::UTF16Encoding.
    See Poco::TextEncoding::queryConvert() and Poco::TextEncoding::sequenceLength()
    for how this is implemented. Contributed by Philippe Cuvillier.
  - fixed SF# 2897650: [branch 1.3.6] Net.SocketAddress won't compile for CYGWIN
  - fixed SF# 2896161: Building on Windows fails when basedir has space in it
  - fixed SF# 2864380: Memory leak when using secure sockets
  - NetSSL_OpenSSL: the SSL/TLS session cache is now disabled by default and
    can be enabled per Context using Poco::Net::Context::enableSessionCache().
  - fixed SF# 2899039: Wrong DST handling in LocalDateTime
  - added Poco::RWLock::ScopedReadLock and Poco::RWLock::ScopedWriteLock (contributed by Marc Chevrier)
  - added Poco::Thread::TID type, as well as Poco::Thread::tid() and Poco::Thread::currentTid()
    to obtain the native thread handle/ID
  - added Zip file comment support
  - On Windows, Poco::SharedLibrary::load() now uses LoadLibraryEx instead of LoadLibrary
    and uses the LOAD_WITH_ALTERED_SEARCH_PATH if an absolute path is specified. This will
    add the directory containing the library to the search path for DLLs that the
    loaded library depends upon.
  - Mac OS X build settings now match those used by default Xcode projects, making linking the
    POCO libs to Xcode projects easier
  - Replaced use of std::valarray in Poco::Net::ICMPEventArgs with std::vector due to issues with
    std::valarray together with STDCXX debug mode on OS X


!!!Release 1.3.5

!!Summary of Changes

  - fixed SF# 2779410: Poco::Data::ODBC::HandleException impovement
  - fixed wrong exception text for Poco::UnhandledException
  - Fixed a problem with SSL shutdown that causes clients (web browsers)
    to hang when the server attempts to perform a clean SSL shutdown. We now call
    SSL_shutdown() once, even if the shutdown is not complete after the first call.
  - added Poco::Crypto::X509Certificate::save()
  - fixed a bug in Poco::Zip::Decompress that results in wrong paths for extracted files
  - fixed a bug in Poco::Zip::ZipManipulator where the Zip file was opened in text format
    on Windows.
  - added Poco::Crypto::X509Certificate::issuedBy() to verify certificate chain.
  - added methods to extract the contents of specific fields from the
    subject and issuer distinguished names of a certificate.
  - fixed 0000089: Thread::sleep() on Linux is extremely inaccurate


!!!Release 1.3.4

!!Summary of Changes

  - fixed SF# 2611804: PropertyFileConfiguration continuation lines
  - fixed SF# 2529788: ServerApplication::beDaemon() broken
  - fixed SF# 2445467: Bug in Thread_WIN32.cpp
  - Improved performance of HTTP Server by removing some
    string copy operations
  - fixed SF# 2310735: HTTPServer: Keep-Alive only works with send()
  - fixed appinf.com IP address in Net testsuite
  - fixed RFC-00188: NumberFormatter and float/double numbers
  - added --pidfile option to ServerApplication on Unix
  - fixed SF# 2499504: Bug in Win32_Thread when using from dll (fixed also for POSIX threads)
  - fixed SF# 2465794: HTTPServerRequestImpl memory leak
  - fixed SF# 2583934: Zip: No Unix permissions set
  - the NetSSL_OpenSSL library has been heavily refactored
  - added NumberFormatter::append*() and DateTimeFormatter::append() functions
  - use NumberFormatter::append() and DateTimeFormatter::append() instead of format() where
    it makes sense to gain some performance
  - added system.dateTime and system.pid to Poco::Util::SystemConfiguration
  - added %F format specifier (fractional seconds/microseconds) to DateTimeFormatter,
    DateTimeParser and PatternFormatter.
  - fixed SF# 2630476: Thread_POSIX::setStackSize() failure with g++ 4.3
  - fixed SF# 2679279: Handling of -- option broken
  - added compile options to reduce memory footprint of statically linked applications
    by excluding various classes from automatically being linked.
    See the POCO_NO_* macros in Poco/Config.h.
  - fixed SF# 2644940: on Windows the COMPUTER-NAME and the HOSTNAME can be different
  - added DNS::hostName() function
  - added build configuration for iPhone (using Apple's SDK)
  - basic support for AIX 5.x/xlC 8
  - fixed a bug resulting in a badly formatted exception message with IOException
    thrown due to a socket-related error
  - fixed SF# 2644718: NetworkInterface name conflict in MinGW
  - added a missing #include to CryptoTransform.h
  - fixed SF# 2635377: HTTPServer::HTTPServer should take AutoPtr<HTTPServerParams>
  - replaced plain pointers with smart pointers in some interfaces
  - upgraded to sqlite 3.6.13
  - improved Data::SQLite error reporting
  - Poco::Glob now works with UTF-8 encoded strings and supports case-insensitive comparison.
    This also fixes SF# 1944831: Glob::glob on windows should be case insensitve
  - added Twitter client sample to Net library
  - Fixed SF# 2513643: Seg fault in Poco::UTF8::toLower on 64-bit Linux
  - Poco::Data::SessionPool: the janitor can be disabled by specifying a zero idle time.
  - added Poco::Data::SessionPool::customizeSession()
  - added support for different SQLite transaction modes (DEFERRED, IMMEDIATE, EXCLUSIVE)
  - fixed a few wrong #if POCO_HAVE_IPv6 in the Net library
  - added support for creating an initialized, but unconnected StreamSocket.
  - added File::isDevice()
  - added family() member function to SocketAddress,
  - Data::SQLite: added support for automatic retries if the database is locked
  - XMLConfiguration is now writable
  - fixed an IPv6 implementation for Windows bug in HostEntry
  - Timer class improvement: interval between callback is no longer influenced by the
    time needed to execute the callback.
  - added PriorityNotificationQueue and TimedNotificationQueue classes to Foundation.
    These are variants of the NotificationQueue class that support priority and
    timestamp-tagged notifications.
  - added Poco::Util::Timer class. This implements a timer that can schedule different
    tasks at different times, using only one thread.
  - the signatures of Poco::NotificationQueue and Poco::NotificationCenter member functions
    have been changed to accept a Poco::Notification::Ptr instead of Poco::Notification*
    to improve exception safety. This change should be transparent and fully backwards
    compatible. The signature of the methods returning a Poco::Notification* have not been
    changed for backwards compatibility. It is recommended, that any Notification* obtained
    should be immediately assigned to a Notification::Ptr.
  - SQLite::SessionImpl::isTransaction() now uses sqlite3_get_autocommit() to find out
    about the transaction state.
  - improved SQLite data type mapping
  - refactored Crypto library to make it independent from NetSSL_OpenSSL.
  - added support for RSA-MD5 digital signatures to Crypto library.
  - removed SSLInitializer from NetSSL library (now moved to Crypto library)
  - added build configs for static libraries to Crypto library
  - OpenSSL now depends on Crypto library (which makes more sense than
    vice versa, as it was before). Poco::Net::X509Certificate is now
    a subclass of Poco::Crypto::X509Certificate (adding the verify()
    member function) and the Poco::Net::SSLInitializer class was
    moved to Poco::Crypto::OpenSSLInitializer.
  - added build configs for static libraries to Zip
  - added batch mode to CppUnit::WinTestRunner.
    WinTestRunnerApp supports a batch mode, which runs the
    test using the standard text-based TestRunner from CppUnit.
    To enable batch mode, start the application with the "/b"
    or "/B" command line argument. Optionally, a path to a file
    where the test output will be written to may be given:
    "/b:<path>" or "/B:<path>".
    When run in batch mode, the exit code of the application
    will denote test success (0) or failure (1).
  - testsuites now also work for static builds on Windows
  - The IPv6 support for Windows now basically works (Net library compiled with POCO_HAVE_IPv6)
  - fixed a potential error when shutting down openssl in a statically linked application
  - added static build configs to Data library
  - added Poco::AtomicCounter class, which uses OS-specific APIs for atomic (thread-safe)
    manipulation of counter values.
  - Poco::RefCountedObject and Poco::SharedPtr now use Poco::AtomicCounter for
    reference counting
  - fixed SF# 2765569: LoadConfiguration failing from current directory


!!Incompatible Changes and Possible Transition Issues

  - Some methods that have previously taken a plain pointer (to a reference counted object)
    as argument now take a Poco::AutoPtr instead. This shouldn't cause any problems for
    properly written code. Examples are Poco::NotificationCenter, Poco::NotificationQueue
    and Poco::Net::HTTPServer.
  - Poco::Glob now works with and assumes UTF-8 encoded strings.
  - Poco::Timer: the interval between callbacks is no longer influenced by the
    time needed to execute the callback.
  - The Crypto and NetSSL_OpenSSL libraries have been refactored. NetSSL_OpenSSL
    now depends on the Crypto library (previously, it was vice versa).


!!!Release 1.3.3

!!Summary of Changes

  - Threads now have optional user-settable stack size (if the OS supports that feature)
  - Events now support simplified delegate syntax based on delegate function template.
    See Poco::AbstractEvent documentation for new syntax.
  - Cache supports new access expire strategy.
  - Upgraded to SQLite 3.6.2
  - Upgraded to PCRE 7.8
  - added HttpOnly support to Poco::Net::HTTPCookie
  - NetworkInterface now has displayName() member (useful only on Windows)
  - Poco::Util::WinRegistryKey now has a read-only mode
  - Poco::Util::WinRegistryKey::deleteKey() can now recursively delete registry keys
  - Poco::File::created() now returns 0 if the creation date/time is not known, as
    it's the case on most Unix platforms (including Linux).
    On FreeBSD and Mac OS X, it returns the real creation time.
  - Time interval based log file rotation (Poco::FileChannel) now works
    correctly. Since there's no reliable and portable way to find out the creation
    date of a file (Windows has the tunneling "feature", most Unixes don't provide
    the creation date), the creation/rotation date of the log file is written into
    the log file as the first line.
  - added Environment::nodeId() for obtaining the Ethernet address of the system
    (this is now also used by UUIDGenerator - the corresponding code from UUIDGenerator
    was moved into Environment)
  - added a release policy argument to SharedPtr template
  - Socket::select() will no longer throw an InvalidArgumentException
    on Windows when called with no sockets at all. If all three socket
    sets are empty, Socket::select() will return 0 immediately.
  - SocketReactor::run() now catches exceptions and reports them via
    the ErrorHandler.
  - SocketReactor has a new IdleNotification, which will be posted when
    the SocketReactor has no sockets to handle.
  - added referenceCount() method to Poco::SharedPtr.
  - POCO now builds with GCC 4.3 (but there are some stupid warnings:
    "suggest parentheses around && within ||".
  - Solution and project files for Visual Studio 2008 are included
  - fixed SF# 1859738: AsyncChannel stall
  - fixed SF# 1815124: XML Compile failed on VS7.1 with XML_UNICODE_WCHAR_T
  - fixed SF# 1867340: Net and NetSSL additional dependency not set - ws2_32.lib
  - fixed SF# 1871946: no exception thrown on error
  - fixed SF# 1881113: LinearHashTable does not conform to stl iterators
  - fixed SF# 1899808: HTMLForm.load() should call clear() first
  - fixed SF# 2030074: Cookie problem with .NET server
  - fixed SF# 2009707: small bug in Net/ICMPPacketImpl.cpp
  - fixed SF# 1988579: Intel Warning: invalid multibyte character sequence
  - fixed SF# 2007486: Please clarify license for Data/samples/*
  - fixed SF# 1985180: Poco::Net::DNS multithreading issue
  - fixed SF# 1968106: DigestOutputStream losing data
  - fixed SF# 1980478: FileChannel loses messages with "archive"="timestamp"
  - fixed SF# 1906481: mingw build WC_NO_BEST_FIT_CHARS is not defined
  - fixed SF# 1916763: Bug in Activity?
  - fixed SF# 1956300: HTTPServerConnection hanging
  - fixed SF# 1963214: Typo in documentation for NumberParser::parseFloat
  - fixed SF# 1981865: Cygwin Makefile lacks ThreadTarget.cpp
  - fixed SF# 1981130: pointless comparison of unsigned integer with zero
  - fixed SF# 1943728: POCO_APP_MAIN namespace issue
  - fixed SF# 1981139: initial value of reference to non-const must be an lvalue
  - fixed SF# 1995073: setupRegistry is broken if POCO_WIN32_UTF8 enabled
  - fixed SF# 1981125: std::swap_ranges overloading resolution failed
  - fixed SF# 2019857: Memory leak in Data::ODBC Extractor
  - fixed SF# 1916761: Bug in Stopwatch?
  - fixed SF# 1951443: NetworkInterface::list BSD/QNX no netmask and broadcast addr
  - fixed SF# 1935310: Unhandled characters in Windows1252Encoding
  - fixed SF# 1948361: a little bug for win32
  - fixed SF# 1896482: tryReadLock intermittent error
  - workaround for SF# 1959059: Poco::SignalHandler deadlock
    the SignalHandler can now be disabled globally by adding a
    #define POCO_NO_SIGNAL_HANDLER to Poco/Config.h
  - fixed SF# 2012050: Configuration key created on read access
  - fixed SF# 1895483: PCRE - possible buffer overflow
  - fixed SF# 2062835: Logfile _creationDate is wrong
  - fixed SF# 2118943: out_of_bound access in Poco::Data::BLOB:rawContent
  - fixed SF# 2121732: Prevent InvalidArgumentException in SocketReactor
  - fixed SF# 1891132: Poco::Data::StatementImpl::executeWithLimit is not correct
  - fixed SF# 1951604: POCO refuses to compile with g++ 4.3.0
  - fixed SF# 1954327: CYGWIN's pthread does not define PTHREAD_STACK_MIN
  - fixed SF# 2124636: Discrepancy between FileWIN32(U)::handleLastError
  - fixed SF# 1558300: MinGW/MSYS Builds
  - fixed SF# 2123266: Memory leak under QNX6 with dinkum library


!!!Release 1.3.2

!!Summary of Changes

  - added POCO_NO_SHAREDMEMORY to Config.h
  - POCO_NO_WSTRING now really disables all wide string related calls
  - added template specialization for string hashfunction (performance)
  - XML parser performance improvements (SAX parser is now up to 40 % faster
  - added parseMemoryNP() to XMLReader and friends
  - URIStreamOpener improvement: redirect logic is now in URIStreamOpener.
    this enables support for redirects from http to https.
  - added support for temporary redirects and useproxy return code
  - added getBlocking() to Socket
  - added File::isHidden()
  - better WIN64 support (AMD64 and IA64 platforms are recognized)
  - added support for timed lock operations to [Fast]Mutex
  - SharedLibrary: dlopen() is called with RTLD_GLOBAL instead of RTLD_LOCAL
    (see http://gcc.gnu.org/faq.html#dso)
  - Poco::Timer threads can now run with a specified priority
  - added testcase for SF# 1774351
  - fixed SF# 1784772: Message::swap omits _tid mem
  - fixed SF# 1790894: IPAddress(addr,family) doesn't fail on invalid address
  - fixed SF# 1804395: Constructor argument name wrong
  - fixed SF# 1806807: XMLWriter::characters should ignore empty strings
  - fixed SF# 1806994: property application.runAsService set too late
  - fixed SF# 1828908: HTMLForm does not encode '+'
  - fixed SF# 1831871: Windows configuration file line endings not correct.
  - fixed SF# 1845545: TCP server hangs on shutdown
  - fixed SF# 1846734: Option::validator() does not behave according to doc
  - fixed SF# 1856567: Assertion in DateTimeParser::tryParse()
  - fixed SF# 1864832: HTTP server sendFile() uses incorrect date
  - HTTPServerResponseImpl now always sets the Date header automatically
    in the constructor.
  - fixed SF# 1787667: DateTimeFormatter and time related classes
    (also SF# 1800031: The wrong behavior of time related classes)
  - fixed SF# 1829700: TaskManager::_taskList contains tasks that never started
  - fixed SF# 1834127: Anonymous enums in Tuple.h result in invalid C++
  - fixed SF# 1834130: RunnableAdapter::operator= not returning a value
  - fixed SF# 1873924: Add exception code to NetException
  - fixed SF# 1873929: SMTPClientSession support for name in sender field
  - logging performance improvements (PatternFormatter)
  - fixed SF# 1883871: TypeList operator < fails for tuples with duplicate values
  - CYGWIN build works again (most things work but Foundation testsuite still fails)
  - new build configuration for Digi Embedded Linux (ARM9, uclibc)
  - new build configuration for PowerPC Linux


!!!Release 1.3.1

!!Summary of Changes

  - DynamicAny fixes for char conversions
  - fixed SF# 1733362: Strange timeout handling in SocketImpl::poll and Socket::select
  - fixed SF patch# 1728912: crash in POCO on Solaris
  - fixed SF# 1732138: Bug in WinRegistryConfiguration::getString
  - fixed SF# 1730790: Reference counting breaks NetworkInterface::list()
  - fixed SF# 1720733: Poco::SignalHandler bug
  - fixed SF# 1718724: Poco::StreamCopier::copyStream loops forever
  - fixed SF# 1718437: HashMap bug
  - changed LinearHashTable iterator implementation. less templates -> good thing.
  - fixed SF# 1733964: DynamicAny compile error
  - UUIDGenerator: fixed infinite loop with non ethernet interfaces
  - updated expat to 2.0.1
  - fixed SF# 1730566: HTTP server throws exception
  - Glob supports symbolic links (additional flag to control behavior)
  - fixed a problem with non blocking connect in NetSSL_OpenSSL
    (see http://www.appinf.com/poco/wiki/tiki-view_forum_thread.php?comments_parentId=441&topics_threshold=0&topics_offset=29&topics_sort_mode=commentDate_desc&topics_find=&forumId=6)
  - fixed a problem with SSL renegotiation in NetSSL_OpenSSL (thanks to Sanjay Chouksey for the fix)
  - fixed SF# 1714753: NetSSL_OpenSSL: HTTPS connections fail with wildcard certs
  - HTTPClientSession: set Host header only if it's not already set (proposed by EHL)
  - NetworkInterface (Windows): Loopback interface now has correct netmask;
    interfaces that do not have an IP address assigned are no longer reported.
  - Fixes for VC++ W4 warnings from EHL
  - SharedMemory: first constructor has an additional "server" parameter
    Setting to true does not unlink the shared memory region when the SharedMemory object is destroyed. (Alessandro Oliveira Ungaro)
  - fixed SF# 1768231: MemoryPool constructor


!!!Release 1.3.0

Release 1.3 of the POCO C++ Libraries contains major improvements and new features throughout all libraries.

!!Summary of Changes

  - Poco::HashMap and Poco::HashSet classes (Foundation)
  - Poco::Tuple class template (Foundation)
  - Poco::SharedMemory class (Foundation)
  - Poco::FileStream, Poco::FileInputStream, Poco::FileOutputStream classes that
    support Unicode (UTF-8) filenames on Windows (Foundation)
  - improvements and bugfixes in the Net library, with a focus on the HTTP client
    and server classes
  - Poco::DynamicAny class (Foundation)
  - improvements to Poco::Net::NetworkInterface class (Net)
  - Poco::Condition class, implementing POSIX condition variable-style
    thread synchronization (Foundation)
  - Poco::RegularExpression now uses [[http://www.pcre.org PCRE]] 7.1 (Foundation)
  - improved Unicode/UTF-8 support -- Poco::Unicode and Poco::UTF8 classes
  - Poco::XML::NodeAppender class for faster DOM tree creation (XML)
  - Poco::Checksum class (Foundation)
  - lots of bugfixes and other improvements -- please see the
    CHANGELOG for details


!!Incompatible Changes and Possible Transition Issues

The (now deprecated) Poco::HashFunction class template has been changed in an
incompatible way. The member function formerly named hash() is now the function
call operator. If you have defined your own HashFunction classes,
you have to update your code. Sorry for the inconvenience.

On Windows, POCO now builds with Unicode/UTF-8 support
(POCO_WIN32_UTF8) enabled by default. If you need the previous (1.2)
behavior, remove the corresponding #define from Poco/Config.h
