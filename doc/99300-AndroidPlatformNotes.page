POCO C++ Libraries Android Platform Notes
AAAIntroduction

!!!Introduction

Starting with release 1.4.2 the POCO C++ Libraries can be used on
Android.
In the past the android NDK used a gmake-based build system. They provided a
a standalone "customized" toolchain. Also they used gcc and the gnu-stl as
build environment. But these are all obsolete.

The newest android NDK is using a cmake based build system with a provided
toolchain for cmake. Also they use now clang as compiler and the libc++ as
standard library.

!!!Requirements

To build Poco for android you need as minimum the android NDK plus cmake executable.
You can download the newest NDK from: https://developer.android.com/ndk and
cmake from http://www.cmake.org or if you have already installed Android SDK,
then install the NDK and cmake executable via the <*Android SDK Manager*>.

!!!Quick start

<path_to_android_sdk>/cmake/<cmake_version>/bin/cmake
-H<path_to_poco_sourcen>
-B/tmp/poco-build
-G'Ninja'
-DCMAKE_BUILD_TYPE=Release
-DCMAKE_MAKE_PROGRAM=<path_to_android_sdk>/cmake/<cmake_version>/bin/ninja
-DCMAKE_TOOLCHAIN_FILE=<path_to_android_sdk>/ndk-bundle/build/cmake/android.toolchain.cmake
-DANDROID_NATIVE_API_LEVEL=24
-DANDROID_ABI=armeabi-v7a

!!!Using Android's Build System

To start with build Poco for android you need following tools/executables:

- cmake executable from the host or Android SDK
- Toolchain for cmake from Android NDK
- Build tool executable make or ninja (ninja-build). Ninja is the prefered way to build and is
also provided by the Android SDK.

If you are using cmake from the Android SDK, then you can found this executable in:
<path_to_android_sdk>/cmake/<cmake_version>/bin/cmake
also the ninja build tool:
<path_to_android_sdk>/cmake/<cmake_version>/bin/ninja

The easiest way is to set the PATH environment to <path_to_android_sdk>/cmake/<cmake_version>/bin
then cmake and ninja is found via the environment.
If you not set the PATH environment you should add -DCMAKE_MAKE_PROGRAM=<path_to_android_sdk>/cmake/<cmake_version>/bin/ninja
as parameter for cmake.

If you have didn't installed the Android SDK, it is also possible to use make as build tool:
<path_to_android_sdk>/ndk-bundle/prebuilt/linux-x86_64/bin/make
or
<path_to_android_sdk>/ndk/<ndk_version>/prebuilt/linux-x86_64/bin/make

The easiest way is to set the PATH environment to <path_to_android_sdk>/ndk/<ndk_version>/prebuilt/linux-x86_64/bin.
If you not set the PATH environment you should add -DCMAKE_MAKE_PROGRAM=<path_to_android_sdk>/ndk/<ndk_version>/prebuilt/linux-x86_64/bin/make
as parameter for cmake.

The toolchain file can be found in the android NDK:
<path_to_android_sdk>/ndk-bundle/build/cmake/android.toolchain.cmake
or
<path_to_android_sdk>/ndk/<ndk_version>/build/cmake/android.toolchain.cmake

To build poco with the default value from the NDK:

cmake -H<path_to_poco_sourcen> -B<path_to_build_dir>/poco_build -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=<path_to_android_sdk>/ndk-bundle/build/cmake/android.toolchain.cmake

The prefered way is to build with ninja:

cmake -H<path_to_poco_sourcen> -B<path_to_build_dir>/poco_build -G'Ninja' -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=<path_to_android_sdk>/ndk-bundle/build/cmake/android.toolchain.cmake

To build with the tools from the Android SDK:

<path_to_android_sdk>/cmake/<cmake_version>/bin/cmake
-H<path_to_poco_sourcen>
-B/tmp/poco-build
-G'Ninja'
-DCMAKE_BUILD_TYPE=Release
-DCMAKE_MAKE_PROGRAM=<path_to_android_sdk>/cmake/<cmake_version>/bin/ninja
-DCMAKE_TOOLCHAIN_FILE=<path_to_android_sdk>/ndk-bundle/build/cmake/android.toolchain.cmake

If you didn't set the <*ANDROID_NATIVE_API_LEVEL*> and <*ANDROID_ABI*> the android toolchain is using the default values.
How to set <*ANDROID_NATIVE_API_LEVEL*> and <*ANDROID_ABI*> and what are the default values you can read this in the Android NDK documentation:
https://developer.android.com/ndk/guides/cmake#options

To set poco build options see in GettingStarted.page#Building using CMake

!!!Restrictions

For the most part, the Linux and Android ports of the POCO C++ Libraries are very similar.
However, there are a few restrictions due to the Binoic C library used by Android.

!!Poco::NamedEvent and Poco::NamedMutex

These classes are not supported on Android. While Poco::NamedEvent and
Poco::NamedMutex objects can be created, any attempt to call a method
of these classes will result in a Poco::NotImplementedException being thrown.

!!Poco::SharedMemory

Shared memory is not supported on Android.

!!Poco::FPEnvironment

The Poco::FPEnvironment class is not available on Android and
cannot be used.

!!Poco::RWLock

On Android, Poco::RWLock is an ordinary mutex.
