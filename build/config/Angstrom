#
# Angstrom
#
# Make settings for Open Embedded/Angstrom
#

#
# General Settings
#
LINKMODE          ?= SHARED
POCO_TARGET_OSNAME = Linux
POCO_TARGET_OSARCH = armv5te
TOOL               = arm-angstrom-linux-gnueabi

#
# Define Tools
#
CC      = $(TOOL)-gcc
CXX     = $(TOOL)-g++
LINK    = $(CXX)
STRIP   = $(TOOL)-strip
LIB     = $(TOOL)-ar -cr
RANLIB  = $(TOOL)-ranlib
SHLIB   = $(CXX) -shared -Wl,-soname,$(notdir $@) -o $@
SHLIBLN = $(POCO_BASE)/build/script/shlibln
DEP     = $(POCO_BASE)/build/script/makedepend.gcc
SHELL   = sh
RM      = rm -rf
CP      = cp
MKDIR   = mkdir -p

#
# Extension for Shared Libraries
#
SHAREDLIBEXT     = .so.$(target_version)
SHAREDLIBLINKEXT = .so

#
# Compiler and Linker Flags
#
CFLAGS          = -Isrc
CFLAGS32        =
CFLAGS64        =
CXXFLAGS        =
CXXFLAGS32      =
CXXFLAGS64      =
LINKFLAGS       =
LINKFLAGS32     =
LINKFLAGS64     =
STATICOPT_CC    =
STATICOPT_CXX   =
STATICOPT_LINK  = -static
SHAREDOPT_CC    = -fPIC
SHAREDOPT_CXX   = -fPIC
SHAREDOPT_LINK  = -Wl,-rpath,$(LIBPATH)
DEBUGOPT_CC     = -g -D_DEBUG
DEBUGOPT_CXX    = -g -D_DEBUG
DEBUGOPT_LINK   = -g
RELEASEOPT_CC   = -O3 -DNDEBUG
RELEASEOPT_CXX  = -O2 -DNDEBUG
RELEASEOPT_LINK = -O2

#
# System Specific Flags
#
SYSFLAGS = -D_XOPEN_SOURCE=600 -D_BSD_SOURCE -D_REENTRANT -D_THREAD_SAFE -DPOCO_NO_FPENVIRONMENT

#
# System Specific Libraries
#
SYSLIBS  = -lpthread -ldl -lrt
