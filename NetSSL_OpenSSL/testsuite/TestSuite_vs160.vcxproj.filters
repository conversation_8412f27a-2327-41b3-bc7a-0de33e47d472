<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="HTTPS">
      <UniqueIdentifier>{172a95d3-8eed-45a8-a0e8-4c7f3bc05032}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Header Files">
      <UniqueIdentifier>{206b0060-72d0-4c53-94d7-ffe6c7081cf0}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Source Files">
      <UniqueIdentifier>{0fa252ca-112c-4ac3-8ff3-b7f1f2035742}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{f182bbab-1b53-49bb-83f2-5730403cc4c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{be8e4b6e-f65e-4566-a456-73c027db100c}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{9104dff9-eb5b-4c4e-8cef-dc61e917fe8f}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{c945c822-fbad-4d15-a60e-ad786aed6af2}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{953fab7a-14f3-41e1-a5ad-5e5bfd849c14}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{264b8241-f74c-4adc-98fe-7d70561e5f3b}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{f9b8804f-6bbe-45b6-bce6-e88ed2a25f8f}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{f902d1b4-e7e7-40c2-8f19-4ff152241069}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer">
      <UniqueIdentifier>{ce11817f-6f52-4de9-81b6-3c29fd13f651}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Header Files">
      <UniqueIdentifier>{60ae15d0-ff20-4bd5-9e73-f3838fbd161e}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Source Files">
      <UniqueIdentifier>{b6803009-2839-45a1-b777-53e81c5e3186}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{5aa25ec3-3dd3-4c49-a830-fc5e327b47de}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{3d16a3f6-62c6-4e22-bf8d-dac4648081f0}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{8350f1a6-6714-4ff9-9968-3e7c659f27bb}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{b75f248c-c67c-4f35-b99a-4e2de83c9bd5}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{a339eb18-8afb-4bfe-a9c1-e6682feeab84}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{8f247295-133e-47c3-9859-cc8c1c0e44d3}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient">
      <UniqueIdentifier>{b0b9b326-182a-4594-8a53-287a3348e99c}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Header Files">
      <UniqueIdentifier>{8154cd88-7d85-493e-a800-69366d5d16f3}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Source Files">
      <UniqueIdentifier>{a6261ec6-e08d-4c71-875f-5c72bfcf310d}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket">
      <UniqueIdentifier>{a01c89f1-e268-4d8d-86fa-2737e47b88b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Header Files">
      <UniqueIdentifier>{76dec4a5-2ee6-4774-aa6c-c2d0bc979bbe}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Source Files">
      <UniqueIdentifier>{6bbfea19-8600-4083-be33-6b343d521e4b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\HTTPSTestServer.h">
      <Filter>HTTPS\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetSSLTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTest.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTestSuite.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTest.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTestSuite.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientSessionTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientTestSuite.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSStreamFactoryTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTest.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTestSuite.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DialogServer.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPSClientSessionTest.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPSClientTestSuite.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTest.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTestSuite.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\HTTPSTestServer.cpp">
      <Filter>HTTPS\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetSSLTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTest.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTestSuite.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTest.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTestSuite.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSessionTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientTestSuite.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactoryTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTest.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTestSuite.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DialogServer.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSClientSessionTest.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSClientTestSuite.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTest.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTestSuite.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>