<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="MongoDB"
	ProjectGUID="{4FF2F34B-7F37-3ACD-AFBC-F21D6D426199}"
	RootNamespace="MongoDB"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include;..\Net\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;MongoDB_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoMongoDBd.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\bin\PocoMongoDBd.pdb"
				SubSystem="1"
				ImportLibrary="..\lib\PocoMongoDBd.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include;..\Net\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;MongoDB_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoMongoDB.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\lib\PocoMongoDB.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include;..\Net\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoMongoDBMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoMongoDBMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include;..\Net\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoMongoDBMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include;..\Net\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoMongoDBMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoMongoDBMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include;..\Net\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoMongoDBMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			>
			<File
				RelativePath=".\src\Array.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Binary.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Connection.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Cursor.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Database.cpp"
				>
			</File>
			<File
				RelativePath=".\src\DeleteRequest.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Document.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Element.cpp"
				>
			</File>
			<File
				RelativePath=".\src\GetMoreRequest.cpp"
				>
			</File>
			<File
				RelativePath=".\src\InsertRequest.cpp"
				>
			</File>
			<File
				RelativePath=".\src\JavaScriptCode.cpp"
				>
			</File>
			<File
				RelativePath=".\src\KillCursorsRequest.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Message.cpp"
				>
			</File>
			<File
				RelativePath=".\src\MessageHeader.cpp"
				>
			</File>
			<File
				RelativePath=".\src\ObjectId.cpp"
				>
			</File>
			<File
				RelativePath=".\src\OpMsgCursor.cpp"
				>
			</File>
			<File
				RelativePath=".\src\OpMsgMessage.cpp"
				>
			</File>
			<File
				RelativePath=".\src\QueryRequest.cpp"
				>
			</File>
			<File
				RelativePath=".\src\RegularExpression.cpp"
				>
			</File>
			<File
				RelativePath=".\src\ReplicaSet.cpp"
				>
			</File>
			<File
				RelativePath=".\src\RequestMessage.cpp"
				>
			</File>
			<File
				RelativePath=".\src\ResponseMessage.cpp"
				>
			</File>
			<File
				RelativePath=".\src\UpdateRequest.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			>
			<File
				RelativePath=".\include\Poco\MongoDB\Array.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Binary.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\BSONReader.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\BSONWriter.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Connection.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Cursor.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Database.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\DeleteRequest.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Document.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Element.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\GetMoreRequest.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\InsertRequest.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\JavaScriptCode.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\KillCursorsRequest.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\Message.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\MessageHeader.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\MongoDB.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\ObjectId.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\PoolableConnectionFactory.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\QueryRequest.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\RegularExpression.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\ReplicaSet.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\RequestMessage.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\ResponseMessage.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\MongoDB\UpdateRequest.h"
				>
			</File>
		</Filter>
		<File
			RelativePath="..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
