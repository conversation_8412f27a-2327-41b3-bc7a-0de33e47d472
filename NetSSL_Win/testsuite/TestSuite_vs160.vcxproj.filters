<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="HTTPS">
      <UniqueIdentifier>{36f4ef21-a9db-40af-a0e7-ea9a3b92a9e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Header Files">
      <UniqueIdentifier>{2782a8e9-0709-4392-bf3a-22ab034fc7ca}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Source Files">
      <UniqueIdentifier>{3309117f-5325-4d2e-8e6d-1fe1faf0bc97}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{68c66010-6d3d-4d3c-bb32-12c1b2747e00}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{3bfa3acb-90b5-40ef-ae01-bcb05c198638}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{3197ab91-b361-4ded-bd71-bc04510db82d}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{995e0ec4-0a8e-4028-8741-828498401966}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{2812877f-2b1e-4933-a05d-446029e4faa6}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{266934b1-0e67-4a5b-9c72-b325a5796d09}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{d6e0a163-3a8a-4a92-92ce-9e77f33cb5fe}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{b6d5a692-81c9-46e3-9981-8f96511c021e}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer">
      <UniqueIdentifier>{68929c63-0d0d-437f-9279-4617dd1f90d4}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Header Files">
      <UniqueIdentifier>{4ee22d8e-a989-4115-9536-c16a4793d8a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Source Files">
      <UniqueIdentifier>{64f69d24-fb76-4449-8c85-3e950351f4b3}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{22d17a5d-ff68-4330-8979-516e8195a67a}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{943d328a-0c15-4ff0-a497-10302133256d}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{fd8022c1-22bd-4625-af5a-10bd378a95bb}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket">
      <UniqueIdentifier>{7dd8cb36-bdb2-42f8-8417-55394d5fdd62}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Header Files">
      <UniqueIdentifier>{9d9fc789-de25-4b65-ab74-b2fbcea13b6d}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Source Files">
      <UniqueIdentifier>{7d900974-e475-4868-a421-05e27a263725}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{d10f11d0-44f3-4926-8117-2403b24bbd58}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{c4a90c4a-ab95-4d8d-9bef-780bdc8225b4}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{e0400fb5-388e-4d4d-aae6-f987e019c57e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\HTTPSTestServer.h">
      <Filter>HTTPS\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetSSLTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTest.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTestSuite.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTest.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTestSuite.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientSessionTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientTestSuite.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSStreamFactoryTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTest.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTestSuite.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTest.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTestSuite.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\HTTPSTestServer.cpp">
      <Filter>HTTPS\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetSSLTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTest.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTestSuite.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTest.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTestSuite.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSessionTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientTestSuite.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactoryTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTest.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTestSuite.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTestSuite.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTest.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>