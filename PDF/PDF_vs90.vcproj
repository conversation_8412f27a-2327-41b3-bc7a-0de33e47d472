<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="PDF"
	ProjectGUID="{E12E5C71-79A4-495A-848F-F1710111E610}"
	RootNamespace="PDF"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;.\include\Poco\PDF;..\Foundation\include;..\XML\include;..\Util\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;_CRT_SECURE_NO_WARNINGS;PDF_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoPDFd.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\bin\PocoPDFd.pdb"
				SubSystem="1"
				ImportLibrary="..\lib\PocoPDFd.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;.\include\Poco\PDF;..\Foundation\include;..\XML\include;..\Util\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;_CRT_SECURE_NO_WARNINGS;PDF_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoPDF.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\lib\PocoPDF.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;.\include\Poco\PDF;..\Foundation\include;..\XML\include;..\Util\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoPDFMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoPDFMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;.\include\Poco\PDF;..\Foundation\include;..\XML\include;..\Util\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoPDFMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;.\include\Poco\PDF;..\Foundation\include;..\XML\include;..\Util\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoPDFMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoPDFMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;.\include\Poco\PDF;..\Foundation\include;..\XML\include;..\Util\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoPDFMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			>
			<File
				RelativePath=".\src\AttributedString.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Cell.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Destination.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Document.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Encoder.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Font.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Image.cpp"
				>
			</File>
			<File
				RelativePath=".\src\LinkAnnotation.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Outline.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Page.cpp"
				>
			</File>
			<File
				RelativePath=".\src\PDFException.cpp"
				>
			</File>
			<File
				RelativePath=".\src\Table.cpp"
				>
			</File>
			<File
				RelativePath=".\src\TextAnnotation.cpp"
				>
			</File>
			<File
				RelativePath=".\src\XMLTemplate.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			>
			<File
				RelativePath=".\include\Poco\PDF\Destination.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Document.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Encoder.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Font.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Image.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\LinkAnnotation.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Outline.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Page.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\PDF.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\PDFException.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\Resource.h"
				>
			</File>
			<File
				RelativePath=".\include\Poco\PDF\TextAnnotation.h"
				>
			</File>
		</Filter>
		<Filter
			Name="3rd Party"
			>
			<Filter
				Name="PNG"
				>
				<Filter
					Name="Header Files"
					>
					<File
						RelativePath=".\include\Poco\PDF\png.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\pngconf.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Source Files"
					>
					<File
						RelativePath=".\src\png.c"
						>
					</File>
					<File
						RelativePath=".\src\pngerror.c"
						>
					</File>
					<File
						RelativePath=".\src\pnggccrd.c"
						>
					</File>
					<File
						RelativePath=".\src\pngget.c"
						>
					</File>
					<File
						RelativePath=".\src\pngmem.c"
						>
					</File>
					<File
						RelativePath=".\src\pngpread.c"
						>
					</File>
					<File
						RelativePath=".\src\pngread.c"
						>
					</File>
					<File
						RelativePath=".\src\pngrio.c"
						>
					</File>
					<File
						RelativePath=".\src\pngrtran.c"
						>
					</File>
					<File
						RelativePath=".\src\pngrutil.c"
						>
					</File>
					<File
						RelativePath=".\src\pngset.c"
						>
					</File>
					<File
						RelativePath=".\src\pngtest.c"
						>
					</File>
					<File
						RelativePath=".\src\pngtrans.c"
						>
					</File>
					<File
						RelativePath=".\src\pngvcrd.c"
						>
					</File>
					<File
						RelativePath=".\src\pngwio.c"
						>
					</File>
					<File
						RelativePath=".\src\pngwrite.c"
						>
					</File>
					<File
						RelativePath=".\src\pngwtran.c"
						>
					</File>
					<File
						RelativePath=".\src\pngwutil.c"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="zlib"
				>
				<Filter
					Name="Header Files"
					>
					<File
						RelativePath=".\include\Poco\PDF\crc32.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\deflate.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\inffast.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\inffixed.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\inflate.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\inftrees.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\trees.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\zconf.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\zlib.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\zutil.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Source Files"
					>
					<File
						RelativePath=".\src\adler32.c"
						>
					</File>
					<File
						RelativePath=".\src\compress.c"
						>
					</File>
					<File
						RelativePath=".\src\crc32.c"
						>
					</File>
					<File
						RelativePath=".\src\deflate.c"
						>
					</File>
					<File
						RelativePath=".\src\infback.c"
						>
					</File>
					<File
						RelativePath=".\src\inffast.c"
						>
					</File>
					<File
						RelativePath=".\src\inflate.c"
						>
					</File>
					<File
						RelativePath=".\src\inftrees.c"
						>
					</File>
					<File
						RelativePath=".\src\trees.c"
						>
					</File>
					<File
						RelativePath=".\src\zutil.c"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="HARU"
				>
				<Filter
					Name="Source Files"
					>
					<File
						RelativePath=".\src\hpdf_3dmeasure.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_annotation.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_array.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_binary.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_boolean.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_catalog.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_destination.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_dict.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_doc.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_doc_png.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encoder.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encoder_cns.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encoder_cnt.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encoder_jp.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encoder_kr.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encoder_utf.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encrypt.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_encryptdict.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_error.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_exdata.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_ext_gstate.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_font.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_font_cid.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_font_tt.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_font_type1.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_base14.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_cid.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_cns.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_cnt.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_jp.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_kr.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_tt.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_fontdef_type1.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_gstate.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_image.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_image_ccitt.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_image_png.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_info.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_list.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_mmgr.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_name.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_namedict.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_null.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_number.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_objects.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_outline.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_page_label.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_page_operator.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_pages.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_pdfa.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_real.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_streams.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_string.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_u3d.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_utils.c"
						>
					</File>
					<File
						RelativePath=".\src\hpdf_xref.c"
						>
					</File>
				</Filter>
				<Filter
					Name="Header Files"
					>
					<File
						RelativePath=".\include\Poco\PDF\hpdf.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_3dmeasure.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_annotation.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_catalog.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_conf.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_consts.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_destination.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_doc.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_encoder.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_encrypt.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_encryptdict.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_error.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_exdata.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_ext_gstate.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_font.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_fontdef.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_gstate.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_image.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_info.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_list.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_mmgr.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_namedict.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_objects.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_outline.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_page_label.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_pages.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_pdfa.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_streams.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_types.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_u3d.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_utils.h"
						>
					</File>
					<File
						RelativePath=".\include\Poco\PDF\hpdf_version.h"
						>
					</File>
				</Filter>
			</Filter>
		</Filter>
		<File
			RelativePath="..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
