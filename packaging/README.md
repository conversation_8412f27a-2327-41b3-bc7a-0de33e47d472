POCO C++ Distribution
=====================

This repository contains packaged Poco releases for various platforms and
successive versions of Poco starting from release Poco-1.6.1.

CHANGELOG contains all changes for the following releases

- Release 1.7.8p4 (2017-08-11)
- Release 1.7.8p3 (2017-06-22)
- Release 1.7.8p2 (2017-04-18)
- Release 1.7.7 (2016-12-31)
- Release 1.7.6 (2016-10-18)
- Release 1.7.5 (2016-08-29)
- Release 1.7.4 (2016-07-20)
- Release 1.7.3 (2016-05-02)
- Release 1.7.2 (2016-03-21)
- Release 1.7.1 (2016-03-14)
- Release 1.7.0 (2016-03-07)
- Release 1.6.1 (2015-08-03)
- Release 1.6.0 (2014-12-22)
- Release 1.5.4 (2014-10-14)
- Release 1.5.3 (2014-06-30)
- Release 1.5.2 (2013-09-16)
- Release 1.5.1 (2013-01-11)
- Release 1.5.0 (2012-10-14)
- Release 1.4.7p1 (2014-11-25)
- Release 1.4.7 (2014-10-06)
- Release 1.4.6p4 (2014-04-18)
- Release 1.4.6p3 (2014-04-02)
- Release 1.4.6p2 (2013-09-16)
- Release 1.4.6p1 (2013-03-06)
- Release 1.4.6 (2013-01-10)
- Release 1.4.5 (2012-11-19)
- Release 1.4.4 (2012-09-03)
- Release 1.4.3p1 (2012-01-23)
- Release 1.4.3 (2012-01-16)
- Release 1.4.2p1 (2011-09-24)
- Release 1.4.2 (2011-08-28)
- Release 1.4.1p1 (2011-02-08)
- Release 1.4.1 (2011-01-29)
- Release 1.4.0 (2010-12-14)
- Release 1.3.6p2 (2010-01-15)
- Release 1.3.6p1 (2009-12-21)
- Release 1.3.6 (2009-11-24)
- Release 1.3.5 (2009-05-11)
- Release 1.3.4 (2009-04-21)
- Release 1.3.3p1 (2008-10-09)
- Release 1.3.3 (2008-10-07)
- Release 1.3.2 (2008-02-04)
- Release 1.3.1 (2007-08-08)
- Release 1.3.0 (2007-05-07)
- Release 1.2.9 (2007-02-26)
- Release 1.2.8 (2007-01-04)
- Release 1.2.7 (2006-12-07)
- Release 1.2.6 (2006-11-19)
- Release 1.2.5 (2006-10-23)
- Release 1.2.4 (2006-10-02)
- Release 1.2.3 (2006-09-14)
- Release 1.2.2 (2006-09-01)
- Release 1.2.1 (2006-08-29)
- Release 1.2.0 (2006-08-29)
- Release 1.1.2 (2006-07-07)
- Release 1.1.1 (2006-04-03)
- Release 1.1.0 (2006-03-23)
- Release 1.1b2 (2006-03-04)
- Release 1.1b1 (2006-03-03)
- Release 1.0.0 (2006-01-19)
- Release 1.0b2 (2006-01-16)
- Release 1.0b1 (2006-01-09)
- Release 1.0a1 (2006-01-03) [internal]
- Release 0.96.1 (2005-12-28)
- Release 0.95.4 (2005-11-07)
- Release 0.95.3 (2005-10-28) [internal]
- Release 0.95.2 (2005-10-22) [internal]
- Release 0.94.1 (2005-09-30) [internal]
- Release 0.93.1 (2005-08-01)
- Release 0.92.1 (2005-05-09)
- Release 0.91.4 (2005-04-11)
- Release 0.91.3 (2005-03-19)
- Release 0.91.2 (2005-02-27)
- Release 0.91.1 (2005-02-21)


 POCO C++ Libraries
==================

POrtable COmponents C++ Libraries are:
--------------------------------------
- A collection of C++ class libraries, conceptually similar to the Java Class Library, the .NET Framework or Apple’s Cocoa.
- Focused on solutions to frequently-encountered practical problems.
- Focused on ‘internet-age’ network-centric applications.
- Written in efficient, modern, 100% ANSI/ISO Standard C++.
- Based on and complementing the C++ Standard Library/STL.
- Highly portable and available on many different platforms.
- Open Source, licensed under the [Boost Software License](https://spdx.org/licenses/BSL-1.0).

----
To start using POCO, see the [Guided Tour](http://pocoproject.org/docs-1.5.3/00100-GuidedTour.html) and [Getting Started](http://pocoproject.org/docs-1.5.3/00200-GettingStarted.html) documents.

----
POCO has an active user and contributing community, please visit our [web site](http://pocoproject.org), [forum](http://pocoproject.org/forum) and [blog](http://pocoproject.org/blog).
Answers to POCO-related questions can also be found on [Stack Overflow](http://stackoverflow.com/questions/tagged/poco-libraries).

----
In regards to Boost, in spite of some functional overlapping,
POCO is best thought of as a Boost complement (rather than replacement).
Side-by-side use of Boost and POCO is a very common occurrence.



