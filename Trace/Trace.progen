vc.project.guid = ${vc.project.guidFromName}
vc.project.name = ${vc.project.baseName}
vc.project.target = Poco${vc.project.name}
vc.project.type = library
vc.project.pocobase = ..
vc.project.outdir = ${vc.project.pocobase}
vc.project.platforms = Win32
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = ${vc.project.name}_vs90.vcproj
vc.project.compiler.include = .\\include\\Poco\\Trace
vc.project.compiler.defines = CPPTRACE_GET_SYMBOLS_WITH_DBGHELP;CPPTRACE_UNWIND_WITH_DBGHELP;CPPTRACE_DEMANGLE_WITH_WINAPI;NOMINMAX
vc.project.compiler.defines.static = CPPTRACE_STATIC_DEFINE
vc.project.compiler.defines.shared = ${vc.project.name}_EXPORTS;cpptrace_lib_EXPORTS
vc.project.compiler.defines.debug_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.defines.release_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.project.linker.dependencies.Win32 = dbghelp.lib
vc.solution.create = true
