<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{a14aaa13-add4-4b58-981f-85f5e99b0507}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{21b68dae-70c1-4f26-8a7e-789d663ab800}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\cpptrace.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ctrace.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\debug_map_resolver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\demangle_with_winapi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\from_current.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\module_base.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\object.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pe.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\safe_dl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\snippet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\symbols_core.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\symbols_with_dbghelp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\symbols_with_nothing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\unwind_with_dbghelp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\unwind_with_nothing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\demangle\demangle.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\platform\dbghelp_syminit_manager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\platform\exception_type.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\platform\path.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\platform\platform.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\platform\program_name.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\snippets\snippet.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\symbols\symbols.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\unwind\unwind.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\common.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\error.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\microfmt.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>