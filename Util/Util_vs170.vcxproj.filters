<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Application">
      <UniqueIdentifier>{061d5385-14a5-451c-bf5e-76ca39dd18e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Application\Header Files">
      <UniqueIdentifier>{28244e6c-0b68-4d8e-bb58-53653a58b5d1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Application\Source Files">
      <UniqueIdentifier>{9bc8d568-e2eb-4771-9b32-ddfddef050eb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration">
      <UniqueIdentifier>{7f37ba93-8f47-42b5-b6f6-06458ac8d1e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration\Header Files">
      <UniqueIdentifier>{cbec2c58-c457-4369-a189-31a7bc1c3a2b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration\Source Files">
      <UniqueIdentifier>{efee542e-1e88-4e3b-8a01-01e2bf10dfd2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options">
      <UniqueIdentifier>{791e1b1f-0123-4954-9cdb-088fb9fc2b7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options\Header Files">
      <UniqueIdentifier>{5fc4cf4d-e28f-4cad-9e65-1f47585ebea0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options\Source Files">
      <UniqueIdentifier>{4127d37e-418f-4b94-85b5-3921c8a86599}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows">
      <UniqueIdentifier>{4b293467-e96b-4939-bd7b-9c34556b5deb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows\Header Files">
      <UniqueIdentifier>{45239924-41ba-4439-b1d6-1eed49716018}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows\Source Files">
      <UniqueIdentifier>{41ec7412-2cf0-4355-969a-6de1162ef20c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util">
      <UniqueIdentifier>{68ae07bf-0f1c-4982-9b67-90d209373425}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util\Header Files">
      <UniqueIdentifier>{c9e42a0c-a847-45f1-a2fa-8b3f014fefdc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util\Source Files">
      <UniqueIdentifier>{a5a132e9-9d19-4eac-85b5-c8c88fc8bc9d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer">
      <UniqueIdentifier>{c4fcb408-8fa2-4891-ba84-9756e0e5e590}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer\Header Files">
      <UniqueIdentifier>{bfb17cc8-93ed-4790-9db2-fa4902c07361}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer\Source Files">
      <UniqueIdentifier>{4afaa3d8-11f8-40c6-a238-4aac5402b66e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Util\Application.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LoggingSubsystem.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\ServerApplication.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Subsystem.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\AbstractConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\ConfigurationMapper.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\ConfigurationView.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\FilesystemConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\IniFileConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\JSONConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LayeredConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LocalConfigurationView.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LoggingConfigurator.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\MapConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\PropertyFileConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\SystemConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\XMLConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\HelpFormatter.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\IntValidator.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Option.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionCallback.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionException.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionProcessor.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionSet.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\RegExpValidator.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Validator.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\WinRegistryConfiguration.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\WinRegistryKey.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\WinService.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Util.h">
      <Filter>Util\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Timer.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\TimerTask.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\TimerTaskAdapter.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Application.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingSubsystem.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ServerApplication.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Subsystem.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AbstractConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationMapper.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationView.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FilesystemConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IniFileConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\JSONConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LayeredConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LocalConfigurationView.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingConfigurator.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MapConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PropertyFileConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SystemConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HelpFormatter.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IntValidator.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Option.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionCallback.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionException.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionProcessor.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionSet.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RegExpValidator.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Validator.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinRegistryConfiguration.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinRegistryKey.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinService.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timer.cpp">
      <Filter>Timer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimerTask.cpp">
      <Filter>Timer\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>