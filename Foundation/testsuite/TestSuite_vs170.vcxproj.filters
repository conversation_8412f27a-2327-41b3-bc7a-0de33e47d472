<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Core">
      <UniqueIdentifier>{26df66ca-110a-487d-83e5-27472b203709}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Source Files">
      <UniqueIdentifier>{c45b25e7-849d-4718-acf1-15d98b2675d3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Header Files">
      <UniqueIdentifier>{26ee74c1-7534-40e3-bcd2-fad456fa5d95}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams">
      <UniqueIdentifier>{7eede610-2135-4bec-8176-98295b74614e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\Source Files">
      <UniqueIdentifier>{dd40e5c7-e59a-4283-8eef-b03c14f4b3c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\Header Files">
      <UniqueIdentifier>{74db690f-db91-43fc-8d87-98ee11911592}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt">
      <UniqueIdentifier>{69725009-5f9b-4e05-b3fb-16a69e5cf3d1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt\Source Files">
      <UniqueIdentifier>{e2d408e8-a79b-40d9-bbbe-696e1958198f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt\Header Files">
      <UniqueIdentifier>{6d665c8b-f17a-4062-8b4c-8b541e238fb1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications">
      <UniqueIdentifier>{4865778f-38b6-4c16-bbd3-fc833af11196}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications\Source Files">
      <UniqueIdentifier>{6aa7bd17-a182-4cba-8bf5-0e90f8d8d36f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications\Header Files">
      <UniqueIdentifier>{ce2775ab-6625-4217-8a74-520fc5e380e9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading">
      <UniqueIdentifier>{e2062d12-9dfb-4383-8ca9-5debe7fd912c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading\Source Files">
      <UniqueIdentifier>{89303991-6699-408d-9042-6070bed6ae49}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading\Header Files">
      <UniqueIdentifier>{49cbd9bc-cbe6-4faa-9c28-dfd175cf1da4}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary">
      <UniqueIdentifier>{0bb92ad4-d4fa-4515-91dd-ec5f33acc270}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary\Source Files">
      <UniqueIdentifier>{ad6bb87c-cc85-4067-bdc6-d0942e7c4b1d}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary\Header Files">
      <UniqueIdentifier>{44903b51-31c8-40ea-925a-6d908dac89e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{30373cef-a7d7-43b8-b6a7-723d9e90519f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{19f9f75e-49cf-45b1-99ce-81aa71715321}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{2ea2b100-92f4-4176-af3e-7d6a70a56154}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem">
      <UniqueIdentifier>{c209aec1-4e9b-4018-9978-9a70baa4c570}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem\Source Files">
      <UniqueIdentifier>{417a4ed1-9a9e-4892-ad9b-b9cb82fd91fc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem\Header Files">
      <UniqueIdentifier>{6e753862-62ef-494a-a323-69e63cf3e94e}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID">
      <UniqueIdentifier>{ff4bc587-400a-4bb0-9f3f-135c8e9bbebd}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID\Source Files">
      <UniqueIdentifier>{328a0dd2-8be4-4e6a-ab9f-de9f3af31a83}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID\Header Files">
      <UniqueIdentifier>{501ca554-cb49-4b9a-9255-ed8b6b71519c}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime">
      <UniqueIdentifier>{fa4d7a28-16ce-4cc1-a268-9340f9c2b392}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime\Source Files">
      <UniqueIdentifier>{c65f9dd7-a631-4512-8be1-fdd54458b7cd}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime\Header Files">
      <UniqueIdentifier>{0e1cdeb5-529e-4fcd-b9a9-f1c73fc1538b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text">
      <UniqueIdentifier>{6dae037e-a3ce-42e3-9d76-69e875fcfa44}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Source Files">
      <UniqueIdentifier>{d6157992-ff8e-436a-8aed-c30b786ca029}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Header Files">
      <UniqueIdentifier>{ecee9773-c23a-486f-9d96-7f921b5e08c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI">
      <UniqueIdentifier>{dad3b7bf-77f7-47c7-acca-fcfd58eb0f9c}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI\Source Files">
      <UniqueIdentifier>{d37b447a-01ce-482f-9a76-6f3a7b9f7cc9}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI\Header Files">
      <UniqueIdentifier>{548ce550-f66b-4305-bf05-384705f466e8}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{b0a65616-ca7f-4c84-b7a1-dd0f7966b3b6}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{2b0f487c-9f8a-409e-ac69-b15073df9213}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{8668af0b-9e47-4c01-bf6f-aff955778816}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{2f4c1147-ded7-440d-8531-8981ab5d4fe2}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{9e2daf2c-da8c-4f54-af9a-49596c495049}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes">
      <UniqueIdentifier>{281ff077-b739-489f-b7a6-fdee8bc614fe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes\Source Files">
      <UniqueIdentifier>{2b8e8287-18fd-44a6-acb1-47bef5def1d2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes\Header Files">
      <UniqueIdentifier>{5e3ae808-86de-4d24-9a9e-789c8f3a26f0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks">
      <UniqueIdentifier>{2833a11b-b10c-4903-8685-9e56c4cbd9fe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks\Source Files">
      <UniqueIdentifier>{afe0e57c-f55b-4ff2-97f4-5a4e43d99758}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks\Header Files">
      <UniqueIdentifier>{966ec9a6-868f-4886-bdd7-79cffbe87e00}</UniqueIdentifier>
    </Filter>
    <Filter Include="Event">
      <UniqueIdentifier>{5c0a0f4e-2299-467b-af5b-fa3ccb720113}</UniqueIdentifier>
    </Filter>
    <Filter Include="Event\Header Files">
      <UniqueIdentifier>{b3f4bf1f-1343-4bfc-a931-de3e31d83015}</UniqueIdentifier>
    </Filter>
    <Filter Include="Event\Source Files">
      <UniqueIdentifier>{fc733aee-fb7b-4b57-b489-e1489ac203fe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache">
      <UniqueIdentifier>{4f0dc829-0a84-49a6-a599-f0f11c707a70}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache\Header Files">
      <UniqueIdentifier>{f8531ea3-5b00-426b-83c3-a1ff9e97a882}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache\Source Files">
      <UniqueIdentifier>{177a96ae-2e74-471b-8008-06271a595819}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing">
      <UniqueIdentifier>{ae8e97ab-f62b-45ce-ba37-96c3519901e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing\Header Files">
      <UniqueIdentifier>{2ca2094f-3cc9-4347-99da-d30147166ff7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing\Source Files">
      <UniqueIdentifier>{44f50e0a-7678-4d55-9407-09ce72197276}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic">
      <UniqueIdentifier>{fc627cf4-2056-4961-996f-27c1fa4a7d39}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\Source Files">
      <UniqueIdentifier>{e046a861-d8e0-482d-b074-5b9fc52eee4b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\Header Files">
      <UniqueIdentifier>{11799d5f-c789-469d-8439-832d412ea38a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AnyTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ArrayTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AutoPtrTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AutoReleasePoolTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ByteOrderTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CoreTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CoreTestSuite.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryIteratorsTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DynamicFactoryTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FormatTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FPETest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ListMapTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MemoryPoolTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedTuplesTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NDCTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NumberFormatterTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NumberParserTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ObjectPoolTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OrderedContainersTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RegularExpressionTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedPtrTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StringTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StringTokenizerTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TuplesTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TypeListTest.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Base32Test.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Base64Test.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\BinaryReaderWriterTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CountingStreamTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FIFOBufferStreamTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileStreamTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HexBinaryTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LineEndingConverterTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MemoryStreamTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NullStreamTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamCopierTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamsTestSuite.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamTokenizerTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TeeStreamTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZLibTest.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptTestSuite.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DigestStreamTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HMACEngineTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MD4EngineTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MD5EngineTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PBKDF2EngineTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RandomStreamTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RandomTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SHA1EngineTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NotificationCenterTest.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NotificationQueueTest.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NotificationsTestSuite.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PriorityNotificationQueueTest.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimedNotificationQueueTest.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ActiveDispatcherTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ActiveMethodTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ActivityTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConditionTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RWLockTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SemaphoreTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadingTestSuite.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadLocalTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadPoolTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimerTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ClassLoaderTest.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ManifestTest.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedLibraryTest.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedLibraryTestSuite.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TestPlugin.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ChannelTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileChannelTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggerTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingFactoryTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingRegistryTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingTestSuite.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LogStreamTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PatternFormatterTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SimpleFileChannelTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TestChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectoryWatcherTest.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FilesystemTestSuite.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileTest.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\GlobTest.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PathTest.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UUIDGeneratorTest.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UUIDTest.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UUIDTestSuite.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ClockTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeFormatterTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeParserTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DateTimeTestSuite.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LocalDateTimeTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StopwatchTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimespanTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimestampTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimezoneTest.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamConverterTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextBufferIteratorTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextConverterTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextEncodingTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextIteratorTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextTestSuite.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UnicodeConverterTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UTF8StringTest.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\URIStreamOpenerTest.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\URITest.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\URITestSuite.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FoundationTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedEventTest.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamedMutexTest.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessRunnerTest.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessesTestSuite.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessTest.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SharedMemoryTest.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TaskManagerTest.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TaskTest.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TaskTestSuite.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\BasicEventTest.cpp">
      <Filter>Event\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DummyDelegate.cpp">
      <Filter>Event\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EventTestSuite.cpp">
      <Filter>Event\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FIFOEventTest.cpp">
      <Filter>Event\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PriorityEventTest.cpp">
      <Filter>Event\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CacheTestSuite.cpp">
      <Filter>Cache\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ExpireCacheTest.cpp">
      <Filter>Cache\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ExpireLRUCacheTest.cpp">
      <Filter>Cache\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LRUCacheTest.cpp">
      <Filter>Cache\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UniqueExpireCacheTest.cpp">
      <Filter>Cache\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UniqueExpireLRUCacheTest.cpp">
      <Filter>Cache\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HashingTestSuite.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HashMapTest.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HashSetTest.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HashTableTest.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LinearHashTableTest.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SimpleHashTableTest.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VarTest.cpp">
      <Filter>Dynamic\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SHA2EngineTest.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DataURIStreamTest.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ActiveThreadPoolTest.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FileStreamRWLockTest.cpp">
      <Filter>Processes\Source Files</Filter>
    <ClCompile Include="src\JSONFormatterTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\AnyTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ArrayTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\AutoPtrTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\AutoReleasePoolTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ByteOrderTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CoreTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CoreTestSuite.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DirectoryIteratorsTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DynamicAnyTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DynamicFactoryTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FormatTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FPETest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ListMapTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MemoryPoolTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NamedTuplesTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NDCTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NumberFormatterTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NumberParserTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ObjectPoolTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OrderedContainersTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\RegularExpressionTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SharedPtrTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StringTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StringTokenizerTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TuplesTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TypeListTest.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\Base32Test.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\Base64Test.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\BinaryReaderWriterTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CountingStreamTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FIFOBufferStreamTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FileStreamTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HexBinaryTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LineEndingConverterTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MemoryStreamTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NullStreamTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StreamCopierTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StreamsTestSuite.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StreamTokenizerTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TeeStreamTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ZLibTest.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CryptTestSuite.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DigestStreamTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HMACEngineTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MD4EngineTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MD5EngineTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PBKDF2EngineTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\RandomStreamTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\RandomTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SHA1EngineTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NotificationCenterTest.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NotificationQueueTest.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NotificationsTestSuite.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PriorityNotificationQueueTest.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimedNotificationQueueTest.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ActiveDispatcherTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ActiveMethodTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ActivityTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ConditionTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\RWLockTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SemaphoreTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ThreadingTestSuite.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ThreadLocalTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ThreadPoolTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ThreadTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimerTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ClassLoaderTest.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ManifestTest.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SharedLibraryTest.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SharedLibraryTestSuite.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TestPlugin.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ChannelTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FileChannelTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LoggerTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LoggingFactoryTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LoggingRegistryTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LoggingTestSuite.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LogStreamTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PatternFormatterTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SimpleFileChannelTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TestChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DirectoryWatcherTest.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FilesystemTestSuite.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FileTest.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\GlobTest.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PathTest.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UUIDGeneratorTest.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UUIDTest.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UUIDTestSuite.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ClockTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DateTimeFormatterTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DateTimeParserTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DateTimeTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DateTimeTestSuite.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LocalDateTimeTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StopwatchTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimespanTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimestampTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TimezoneTest.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\StreamConverterTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TextBufferIteratorTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TextConverterTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TextEncodingTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TextIteratorTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TextTestSuite.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UnicodeConverterTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UTF8StringTest.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\URIStreamOpenerTest.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\URITest.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\URITestSuite.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FoundationTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NamedEventTest.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NamedMutexTest.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ProcessRunnerTest.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ProcessesTestSuite.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ProcessTest.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SharedMemoryTest.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TaskManagerTest.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TaskTest.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TaskTestSuite.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\BasicEventTest.h">
      <Filter>Event\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DummyDelegate.h">
      <Filter>Event\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\EventTestSuite.h">
      <Filter>Event\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FIFOEventTest.h">
      <Filter>Event\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PriorityEventTest.h">
      <Filter>Event\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CacheTestSuite.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ExpireCacheTest.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ExpireLRUCacheTest.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LRUCacheTest.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UniqueExpireCacheTest.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UniqueExpireLRUCacheTest.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HashingTestSuite.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HashMapTest.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HashSetTest.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HashTableTest.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LinearHashTableTest.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SimpleHashTableTest.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\VarTest.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SHA2EngineTest.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DataURIStreamTest.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ActiveThreadPoolTest.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FileStreamRWLockTest.h">
      <Filter>Processes\Header Files</Filter>
    <ClInclude Include="src\JSONFormatterTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
