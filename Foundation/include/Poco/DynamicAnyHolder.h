//
// DynamicAnyHolder.h
//
// Library: Foundation
// Package: Dynamic
// Module:  VarHolder
//
// Forward header for VarHolder class to maintain backward compatibility
//
// Copyright (c) 2007, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#ifndef Foundation_DynamicAnyHolder_INCLUDED
#define Foundation_DynamicAnyHolder_INCLUDED

//@ deprecated
#include "Poco/Dynamic/VarHolder.h"


#endif // Foundation_DynamicAnyHolder_INCLUDED
