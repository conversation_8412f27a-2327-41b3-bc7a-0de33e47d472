#
# Makefile
#
# Makefile for Poco Encodings
#

include $(POCO_BASE)/build/rules/global

objects = \
	DoubleByteEncoding \
	Encodings \
	ISO8859_10Encoding \
	ISO8859_11Encoding \
	ISO8859_13Encoding \
	ISO8859_14Encoding \
	ISO8859_16Encoding \
	ISO8859_3Encoding \
	ISO8859_4Encoding \
	ISO8859_5Encoding \
	ISO8859_6Encoding \
	ISO8859_7Encoding \
	ISO8859_8Encoding \
	ISO8859_9Encoding \
	Windows1253Encoding \
	Windows1254Encoding \
	Windows1255Encoding \
	Windows1256Encoding \
	Windows1257Encoding \
	Windows1258Encoding \
	Windows874Encoding \
	Windows932Encoding \
	Windows936Encoding \
	Windows949Encoding \
	Windows950Encoding \
	MacRomanEncoding \
	MacCentralEurRomanEncoding \
	MacCyrillicEncoding \
	MacChineseTradEncoding \
	MacChineseSimpEncoding \
	MacJapaneseEncoding \
	MacKoreanEncoding

target         = PocoEncodings
target_version = $(LIBVERSION)
target_libs    = PocoFoundation

include $(POCO_BASE)/build/rules/lib
