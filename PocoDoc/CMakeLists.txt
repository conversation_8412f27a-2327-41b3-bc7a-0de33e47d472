# Sources
file(GLOB SRCS_G "src/*.cpp")
POCO_SOURCES_AUTO(SRCS ${SRCS_G})

add_executable(PocoDoc ${SRCS})

target_link_libraries(PocoDoc PUBLIC Poco::Util Poco::XML Poco::CppParser Poco::DataSQLite Poco::Data)

if(ENABLE_DATA_SQLITE_FTS5)
	target_compile_definitions(PocoDoc PRIVATE POCO_ENABLE_SQLITE_FTS5)
endif()
 

install(
	TARGETS PocoDoc EXPORT PocoDocTargets
	LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
	ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
	RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
	BUNDLE DESTINATION ${CMAKE_INSTALL_BINDIR}
	INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)
