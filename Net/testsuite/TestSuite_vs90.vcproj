<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="TestSuite"
	ProjectGUID="{D5EFBF27-B934-4B8D-8AE5-6EC00374819C}"
	RootNamespace="TestSuite"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\TestSuited.exe"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_mt\TestSuited.exe"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				IgnoreDefaultLibraryNames="nafxcwd.lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_mt\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_mt\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				IgnoreDefaultLibraryNames="nafxcw.lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_md\TestSuited.exe"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_md\TestSuited.pdb"
				SubSystem="1"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			UseOfMFC="0"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="..\include;..\..\CppUnit\include;..\..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_md\TestSuite.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="NetCore"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\DNSTest.h"
					>
				</File>
				<File
					RelativePath=".\src\IPAddressTest.h"
					>
				</File>
				<File
					RelativePath=".\src\NetCoreTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\NetworkInterfaceTest.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketAddressTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\DNSTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\IPAddressTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NetCoreTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NetworkInterfaceTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketAddressTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="_Suite"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\NetTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\NetTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="_Driver"
			>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\Driver.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Sockets"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\DatagramSocketTest.h"
					>
				</File>
				<File
					RelativePath=".\src\DialogServer.h"
					>
				</File>
				<File
					RelativePath=".\src\DialogSocketTest.h"
					>
				</File>
				<File
					RelativePath=".\src\EchoServer.h"
					>
				</File>
				<File
					RelativePath=".\src\MulticastEchoServer.h"
					>
				</File>
				<File
					RelativePath=".\src\MulticastSocketTest.h"
					>
				</File>
				<File
					RelativePath=".\src\PollSetTest.h"
					>
				</File>
				<File
					RelativePath=".\src\RawSocketTest.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketsTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketStreamTest.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketTest.h"
					>
				</File>
				<File
					RelativePath=".\src\UDPEchoServer.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\DatagramSocketTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DialogServer.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DialogSocketTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EchoServer.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MulticastEchoServer.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MulticastSocketTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\PollSetTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RawSocketTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketsTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketStreamTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\UDPEchoServer.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Messages"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\MediaTypeTest.h"
					>
				</File>
				<File
					RelativePath=".\src\MessageHeaderTest.h"
					>
				</File>
				<File
					RelativePath=".\src\MessagesTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\MultipartReaderTest.h"
					>
				</File>
				<File
					RelativePath=".\src\MultipartWriterTest.h"
					>
				</File>
				<File
					RelativePath=".\src\NameValueCollectionTest.h"
					>
				</File>
				<File
					RelativePath=".\src\QuotedPrintableTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\MediaTypeTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MessageHeaderTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MessagesTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MultipartReaderTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MultipartWriterTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NameValueCollectionTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\QuotedPrintableTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="HTTP"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\HTTPCookieTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPCredentialsTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPRequestTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPResponseTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPTestServer.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\NTLMCredentialsTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\HTTPCookieTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPCredentialsTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPRequestTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPResponseTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPTestServer.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NTLMCredentialsTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="TCPServer"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\TCPServerTest.h"
					>
				</File>
				<File
					RelativePath=".\src\TCPServerTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\TCPServerTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\TCPServerTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="HTTPServer"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\HTTPServerTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPServerTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\HTTPServerTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPServerTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="HTML"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\HTMLFormTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTMLTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\HTMLFormTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTMLTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="HTTPClient"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\HTTPClientSessionTest.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPClientTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\HTTPStreamFactoryTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\HTTPClientSessionTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPClientTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\HTTPStreamFactoryTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="FTPClient"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\FTPClientSessionTest.h"
					>
				</File>
				<File
					RelativePath=".\src\FTPClientTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\FTPStreamFactoryTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\FTPClientSessionTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\FTPClientTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\FTPStreamFactoryTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Reactor"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\ReactorTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketConnectorTest.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketReactorTest.h"
					>
				</File>
				<File
					RelativePath=".\src\SocketProactorTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\ReactorTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketConnectorTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketReactorTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SocketProactorTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Mail"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\MailMessageTest.h"
					>
				</File>
				<File
					RelativePath=".\src\MailStreamTest.h"
					>
				</File>
				<File
					RelativePath=".\src\MailTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\POP3ClientSessionTest.h"
					>
				</File>
				<File
					RelativePath=".\src\SMTPClientSessionTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\MailMessageTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MailStreamTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MailTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\POP3ClientSessionTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SMTPClientSessionTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="ICMP"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\ICMPClientTest.h"
					>
				</File>
				<File
					RelativePath=".\src\ICMPClientTestSuite.h"
					>
				</File>
				<File
					RelativePath=".\src\ICMPSocketTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\ICMPClientTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ICMPClientTestSuite.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ICMPSocketTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="NTP"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\NTPClientTest.h"
					>
				</File>
				<File
					RelativePath=".\src\NTPClientTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\NTPClientTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NTPClientTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Logging"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\SyslogTest.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\SyslogTest.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="WebSocket"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\WebSocketTest.h"
					>
				</File>
				<File
					RelativePath=".\src\WebSocketTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\WebSocketTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\WebSocketTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="OAuth"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\OAuth10CredentialsTest.h"
					>
				</File>
				<File
					RelativePath=".\src\OAuth20CredentialsTest.h"
					>
				</File>
				<File
					RelativePath=".\src\OAuthTestSuite.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\OAuth10CredentialsTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\OAuth20CredentialsTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\OAuthTestSuite.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="UDP"
			>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\UDPServerTest.cpp"
					>
				</File>
				<File
					RelativePath=".\src\UDPServerTestSuite.cpp"
					>
				</File>
			</Filter>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\src\UDPServerTest.h"
					>
				</File>
				<File
					RelativePath=".\src\UDPServerTestSuite.h"
					>
				</File>
			</Filter>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>