<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="NetCore">
      <UniqueIdentifier>{cfe84064-1b49-4f59-b5b8-45c6559794d9}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Header Files">
      <UniqueIdentifier>{bd859a8d-9506-4552-a52e-5bcf1da5d9be}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Source Files">
      <UniqueIdentifier>{3fcf67c3-c181-4bbe-be37-5bb84a6a84c7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets">
      <UniqueIdentifier>{3d5ef02e-0dbd-484f-ab83-4eb0320e3fa1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Header Files">
      <UniqueIdentifier>{aa38aada-4e99-4245-9a88-280b518521e7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Source Files">
      <UniqueIdentifier>{21f1bd5b-6628-4418-b1f2-04fb2cc1eb01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages">
      <UniqueIdentifier>{98be0804-9e75-4c49-ba89-9757c22af3e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Header Files">
      <UniqueIdentifier>{bba1b300-b121-4280-8848-e880efb3d63b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Source Files">
      <UniqueIdentifier>{cfe6f937-9c33-4549-8410-62e0eb6d383a}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP">
      <UniqueIdentifier>{63ef2709-c79a-4759-b129-97c840d23db0}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Header Files">
      <UniqueIdentifier>{7d385e57-c285-4ee4-ab53-af6c4ac206e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Source Files">
      <UniqueIdentifier>{8740010f-a528-438a-8273-81c84db4ca6f}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{bf936efc-d467-460d-a2dd-64450332a886}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{ebc3180b-463a-4eb4-b4fa-cdfdd4bffcc0}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{9b8f52ff-0c03-4187-b3cb-3b26144b1acc}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer">
      <UniqueIdentifier>{3f3c49c8-dc3b-49a8-9425-8c4d9b017ff1}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Header Files">
      <UniqueIdentifier>{e80ec80a-4e7b-49fe-9404-7721b040a476}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Source Files">
      <UniqueIdentifier>{02574d27-29dc-41ea-b27d-906e01dac20e}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient">
      <UniqueIdentifier>{e7f4bef2-23bf-4f57-b4f8-c38bf27619b2}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Header Files">
      <UniqueIdentifier>{89595039-e564-4558-a7f6-7cbb79e98422}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Source Files">
      <UniqueIdentifier>{b8766c85-4254-49cb-b77d-372a80d41d61}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML">
      <UniqueIdentifier>{b4977bbf-a8ba-48b8-849f-6c6d114b5561}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Header Files">
      <UniqueIdentifier>{9d3ec38f-5510-4ea4-9f3e-eb4f3a181488}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Source Files">
      <UniqueIdentifier>{196fdf22-95f9-4570-8152-fc4c7a9eef57}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient">
      <UniqueIdentifier>{0469c687-fcad-46a5-8ea5-04a1788ecd43}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient\Header Files">
      <UniqueIdentifier>{c2f11e8b-d583-46aa-8e08-a69117968f65}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient\Source Files">
      <UniqueIdentifier>{8fbfb882-7cd3-4138-a5be-dac696756ed5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor">
      <UniqueIdentifier>{87c28266-22ef-4cf7-a838-e9482619784a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Header Files">
      <UniqueIdentifier>{be268ec5-c655-4521-9dfc-e87d8a19b31e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Source Files">
      <UniqueIdentifier>{0fac09e2-ecd0-4662-9046-01cdb875b9c9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{37398fd7-3892-41ed-a4c2-fde9bf634066}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{9d0e9783-1e03-4e38-8cf2-91eadea8a0da}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{4bf3be1e-fda8-4e4a-bdc9-9b3f9dd47067}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP">
      <UniqueIdentifier>{d7b06e6c-156a-426e-ae12-8dee212805fd}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Header Files">
      <UniqueIdentifier>{f13b0323-f425-46be-91c7-84fc8e38485e}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Source Files">
      <UniqueIdentifier>{6f6c7570-df08-46cb-8a3d-609ef8a0f674}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP">
      <UniqueIdentifier>{d05a0b70-c2dc-4f89-abd1-cc990ea2be69}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Header Files">
      <UniqueIdentifier>{e25c0013-9cb4-46a9-9220-c7857417609a}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Source Files">
      <UniqueIdentifier>{ec2feedd-a8bd-4be5-806e-30c74c97c8c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{2d785b1e-be4a-4681-af70-9d5bc70a76a5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{36d7cfaa-5502-4578-804f-a33845842799}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{078b3fb0-5b03-4e9d-84a4-dd38dddec5eb}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{c642fe57-9699-42f7-bf37-a2c202863115}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{55d3c6bf-052f-4a29-80a5-ea601795d297}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{d8c45bba-ba27-4809-b89e-fc600e2633f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth">
      <UniqueIdentifier>{c72ec9ae-89b8-4221-b2d3-d8e1e6d5f2f6}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Header Files">
      <UniqueIdentifier>{6d48c35b-f053-4b9c-86c0-7fed4ef93689}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Source Files">
      <UniqueIdentifier>{a64ac6ce-5cb4-4c7c-b6be-a50d86d033d1}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP">
      <UniqueIdentifier>{b21e3bb4-cea3-4597-8be1-dbb8ef37fc22}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Source Files">
      <UniqueIdentifier>{b6df8cdb-420d-45d4-898d-3ecf3d79bb60}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Header Files">
      <UniqueIdentifier>{9990538b-db3d-4d71-8211-01a6064230a2}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM">
      <UniqueIdentifier>{2b73d1cc-7a88-46b5-a721-f3a0e0fd2b29}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM\Header Files">
      <UniqueIdentifier>{c4b4a4c1-3019-4bb7-bd49-f76d1de1ba87}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM\Source Files">
      <UniqueIdentifier>{086a387c-0eff-47cf-9760-6961d92f28f9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Net\DNS.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HostEntry.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\IPAddress.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\IPAddressImpl.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Net.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetException.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetworkInterface.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketAddress.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketAddressImpl.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketDefs.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\DatagramSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\DatagramSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\DialogSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MulticastSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PollSet.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RawSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RawSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ServerSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ServerSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Socket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketStream.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\StreamSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\StreamSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\wepoll.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FilePartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MediaType.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MessageHeader.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MultipartReader.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MultipartWriter.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NameValueCollection.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NullPartHandler.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PartHandler.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PartStore.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\QuotedPrintableDecoder.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\QuotedPrintableEncoder.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\StringPartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPAuthenticationParams.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPBasicCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPBasicStreamBuf.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPBufferAllocator.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPChunkedStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPCookie.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPDigestCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPFixedLengthStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPHeaderStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPMessage.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPNTLMCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPRequest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPResponse.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSession.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServer.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerConnection.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerConnectionFactory.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerDispatcher.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\TCPServerParams.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\AbstractHTTPRequestHandler.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPRequestHandler.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPRequestHandlerFactory.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServer.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerConnection.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerConnectionFactory.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerParams.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerRequest.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerRequestImpl.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerResponse.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerResponseImpl.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPServerSession.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPClientSession.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPIOStream.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSessionFactory.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSessionInstantiator.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPStreamFactory.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTMLForm.h">
      <Filter>HTML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPClientSession.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPStreamFactory.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ParallelSocketAcceptor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ParallelSocketReactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketAcceptor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketConnector.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketNotification.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketNotifier.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketReactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SocketProactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MailMessage.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MailRecipient.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MailStream.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\POP3ClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPClient.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPEventArgs.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPPacket.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPPacketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPSocket.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPSocketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ICMPv4PacketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTPClient.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTPEventArgs.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTPPacket.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RemoteSyslogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RemoteSyslogListener.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SMTPChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\WebSocket.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\WebSocketImpl.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\OAuth10Credentials.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\OAuth20Credentials.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\MultiSocketPoller.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SingleSocketPoller.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPClient.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPHandler.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPServer.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPServerParams.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\UDPSocketReader.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NTLMCredentials.h">
      <Filter>NTLM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSPINTLMCredentials.h">
      <Filter>NTLM\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\DNS.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HostEntry.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IPAddress.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IPAddressImpl.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Net.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetException.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetworkInterface.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketAddress.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketAddressImpl.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DatagramSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DatagramSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DialogSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MulticastSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PollSet.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RawSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RawSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ServerSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ServerSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Socket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketStream.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StreamSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\wepoll.c">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FilePartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MediaType.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MessageHeader.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MultipartReader.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MultipartWriter.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NameValueCollection.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NullPartHandler.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartHandler.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartStore.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\QuotedPrintableDecoder.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\QuotedPrintableEncoder.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StringPartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPAuthenticationParams.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPBasicCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPBufferAllocator.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPChunkedStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPCookie.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPDigestCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPFixedLengthStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPHeaderStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPMessage.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPNTLMCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPResponse.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSession.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServer.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerConnection.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerConnectionFactory.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerDispatcher.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerParams.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AbstractHTTPRequestHandler.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequestHandler.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequestHandlerFactory.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServer.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerConnection.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerConnectionFactory.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerParams.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerRequest.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerRequestImpl.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerResponse.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerResponseImpl.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerSession.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPClientSession.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPIOStream.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSessionFactory.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSessionInstantiator.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPStreamFactory.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTMLForm.cpp">
      <Filter>HTML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPClientSession.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPStreamFactory.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketNotification.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketNotifier.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketReactor.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketProactor.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailMessage.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailRecipient.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailStream.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\POP3ClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPClient.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPEventArgs.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPPacket.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPPacketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPSocket.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPSocketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPv4PacketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPClient.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPEventArgs.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPPacket.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RemoteSyslogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RemoteSyslogListener.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SMTPChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocket.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketImpl.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuth10Credentials.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuth20Credentials.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPClient.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPServerParams.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTLMCredentials.cpp">
      <Filter>NTLM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSPINTLMCredentials.cpp">
      <Filter>NTLM\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>