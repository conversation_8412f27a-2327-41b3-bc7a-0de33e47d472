
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{44776970-6192-36A4-85DB-C0E286224D74}"
	ProjectSection(ProjectDependencies) = postProject
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6} = {DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3} = {BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PocoDataSQLiteExternal", "PocoDataSQLiteExternal.vcxproj", "{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}"
	ProjectSection(ProjectDependencies) = postProject
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3} = {BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{44776970-6192-36A4-85DB-C0E286224D74}.Debug|x64.ActiveCfg = Debug|x64
		{44776970-6192-36A4-85DB-C0E286224D74}.Release|x64.ActiveCfg = Release|x64
		{44776970-6192-36A4-85DB-C0E286224D74}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{44776970-6192-36A4-85DB-C0E286224D74}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.Debug|x64.ActiveCfg = Debug|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.Debug|x64.Build.0 = Debug|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.Release|x64.ActiveCfg = Release|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.Release|x64.Build.0 = Release|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DDFAAA68-F497-31DC-92AF-1373AC4EA6D6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.Debug|x64.ActiveCfg = Debug|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.Debug|x64.Build.0 = Debug|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.Release|x64.ActiveCfg = Release|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.Release|x64.Build.0 = Release|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BFDE63A5-BF22-3A39-90BF-BD99D880D1C3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DFBEA8BC-219F-3976-8ACE-F9F50D18F6BC}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
