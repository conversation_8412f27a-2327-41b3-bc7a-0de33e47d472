<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SQLite">
      <UniqueIdentifier>{ae0dae38-b1e2-4c25-8986-ffb7a71d63d2}</UniqueIdentifier>
    </Filter>
    <Filter Include="SQLite\Header Files">
      <UniqueIdentifier>{967a7f18-16d5-4138-963c-14f5581c1d06}</UniqueIdentifier>
    </Filter>
    <Filter Include="SQLite\Source Files">
      <UniqueIdentifier>{750e3590-154f-43ef-922f-fb208184f6cb}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty">
      <UniqueIdentifier>{b491a2c6-9895-4ba1-b059-50b156e55b0e}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\Header Files">
      <UniqueIdentifier>{518b3aba-5a41-47bc-b125-23bc694773a9}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\Source Files">
      <UniqueIdentifier>{930ce918-f656-4d48-ab21-addd59889448}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Data\SQLite\Binder.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Connector.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Extractor.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Notifier.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SessionImpl.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SQLite.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SQLiteException.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SQLiteStatementImpl.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Utility.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\sqlite3.h">
      <Filter>3rdparty\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Binder.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Connector.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Extractor.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Notifier.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SessionImpl.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SQLiteException.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SQLiteStatementImpl.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\sqlite3.c">
      <Filter>3rdparty\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\DLLVersion.rc" />
  </ItemGroup>
</Project>