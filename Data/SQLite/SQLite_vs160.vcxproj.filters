<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SQLite">
      <UniqueIdentifier>{100e33ce-9d1d-4aa8-a695-8a142faf8823}</UniqueIdentifier>
    </Filter>
    <Filter Include="SQLite\Header Files">
      <UniqueIdentifier>{77206dbd-501e-4814-8778-66aec54307c6}</UniqueIdentifier>
    </Filter>
    <Filter Include="SQLite\Source Files">
      <UniqueIdentifier>{5fcf0bdd-b676-4729-900c-bc7f1383a543}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty">
      <UniqueIdentifier>{85efd7ee-ee09-40d2-acd1-3a29ed7439f6}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\Header Files">
      <UniqueIdentifier>{b862c44f-e276-42f3-b078-837cf482a0c1}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\Source Files">
      <UniqueIdentifier>{9d999f10-40c8-408d-bc98-354804d153eb}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Data\SQLite\Binder.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Connector.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Extractor.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Notifier.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SessionImpl.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SQLite.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SQLiteException.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\SQLiteStatementImpl.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\SQLite\Utility.h">
      <Filter>SQLite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\sqlite3.h">
      <Filter>3rdparty\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Binder.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Connector.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Extractor.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Notifier.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SessionImpl.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SQLiteException.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SQLiteStatementImpl.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>SQLite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\sqlite3.c">
      <Filter>3rdparty\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\DLLVersion.rc" />
  </ItemGroup>
</Project>