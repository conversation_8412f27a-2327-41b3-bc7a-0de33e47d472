//
// Position.cpp
//
// Library: Data
// Package: DataCore
// Module:  Position
//
// Copyright (c) 2006, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#include "Poco/Data/Position.h"


namespace Poco {
namespace Data {


Position::Position(Poco::UInt32 value): _value(value)
{
}


Position::~Position()
{
}


} } // namespace Poco::Data
