<%@ page class="TestHandler" namespace="AI::Test" session="test" %>
<%!
#include "Poco/DateTime.h"
#include "Poco/DateTimeFormatter.h"
#include "Poco/DateTimeFormat.h"


using Poco::DateTime;
using Poco::DateTimeFormatter;
using Poco::DateTimeFormat;
%>

<%
	DateTime now;
	std::string dt(DateTimeFormatter::format(now, DateTimeFormat::SORTABLE_FORMAT));
%>
<html>
<head>
<title>Test Page</title>
</head>
<%-- This is a server-side comment --%>
<body>
<h1>Hello, World!</h1>
<p><%= dt %></p>
</body>
</html>
