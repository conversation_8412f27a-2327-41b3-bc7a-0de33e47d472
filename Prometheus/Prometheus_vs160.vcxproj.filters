<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Prometheus">
      <UniqueIdentifier>{ea24b5dd-d6e5-452c-bd25-81fad9e167d9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Prometheus\Header Files">
      <UniqueIdentifier>{384be2e8-8144-457b-a2d9-7f1407e6f087}</UniqueIdentifier>
    </Filter>
    <Filter Include="Prometheus\Source Files">
      <UniqueIdentifier>{2a43c175-e4a3-4423-bce9-c381d34797db}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Prometheus\AtomicFloat.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\CallbackMetric.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Collector.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Counter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Exporter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Gauge.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Histogram.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\IntCounter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\IntGauge.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\LabeledMetric.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\LabeledMetricImpl.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Metric.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\MetricsRequestHandler.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\MetricsServer.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\ProcessCollector.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Prometheus.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\Registry.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\TextExporter.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Prometheus\ThreadPoolCollector.h">
      <Filter>Prometheus\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Collector.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Counter.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Gauge.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Histogram.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IntCounter.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IntGauge.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LabeledMetric.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MetricsRequestHandler.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MetricsServer.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessCollector.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Registry.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextExporter.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ThreadPoolCollector.cpp">
      <Filter>Prometheus\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>